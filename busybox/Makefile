#
# compile and install busybox in ../ramdisk/
#
TOPDIR=..

BUSYBOX_VER=1.35.0
BUSYBOX_HOME=busybox-$(BUSYBOX_VER)

all: prepare $(BUSYBOX_HOME)/.config
	make -C $(BUSYBOX_HOME) CFLAGS="-fstack-protector-strong -fPIE" \
		LDFLAGS="-Wl,-z,relro,-z,now -pie -Wl,-z,noexecstack" 
	make -C $(BUSYBOX_HOME) install CFLAGS="-fstack-protector-strong -fPIE" \
		LDFLAGS="-Wl,-z,relro,-z,now -pie -Wl,-z,noexecstack" || exit 1
prepare:
	@if [ ! -d $(BUSYBOX_HOME) ]; then			\
		tar -xf busybox-$(BUSYBOX_VER).tar.bz2;		\
		cd $(BUSYBOX_HOME); 				\
		patch -p1 -i ../$(BUSYBOX_HOME).patch;		\
		cd -;						\
	fi

$(BUSYBOX_HOME)/.config: busybox-config-$(BUSYBOX_VER)
	cp $< $@

clean:
	rm -rf $(BUSYBOX_HOME)

depclean: clean


.PHONY: prepare all clean depclean


