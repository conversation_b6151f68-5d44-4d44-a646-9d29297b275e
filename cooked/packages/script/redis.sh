#!/bin/bash

PATH=/bin:/sbin:/usr/bin:/usr/sbin:/sbin:$PATH

TOPDIR=$(realpath ..)
REDIS=redis-7.2.4
REDIS_SRC_DIR=${TOPDIR}/packages/${REDIS}
REDIS_TARGET=redis-7.2.4.tar.gz
ASD_PREFIX_DIR=/cooked/$REDIS
PREFIX_SAVE_DIR=${TOPDIR}/redis



build_redis() {
	echo "Start build $REDIS ... "

	if [ -d $REDIS_SRC_DIR ]; then
		rm -rf $REDIS
	fi

    if [ -d $REDIS ]; then
		rm -rf $REDIS
	fi
	tar -xf ${REDIS_TARGET}

	cd $REDIS

    make  || exit 1

    cd ..

    cp ${REDIS_SRC_DIR}/src/redis-cli ${PREFIX_SAVE_DIR}
    cp ${REDIS_SRC_DIR}/src/redis-server ${PREFIX_SAVE_DIR}


	rm -rf $REDIS
}

build_redis
