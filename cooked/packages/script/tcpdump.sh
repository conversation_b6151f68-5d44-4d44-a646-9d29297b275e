#!/bin/bash

PATH=/bin:/sbin:/usr/bin:/usr/sbin:/sbin:$PATH

TOPDIR=$(realpath ..)
FTS_LIBPCAP=libpcap-1.10.1
FTS_LIBPCAP_TARGET=libpcap-1.10.1.tar.gz
LIBPCAP_PREFIX_DIR=${TOPDIR}/libpcap
FTS_TCPDUMP=tcpdump-4.99.4
FTS_TCPDUMP_TARGET=tcpdump-4.99.4.tar.gz
TCPDUMP_PREFIX_DIR=${TOPDIR}/tcpdump

build_tcpdump() {
	echo "Start build $FTS_TCPDUMP ... "

	if [ ! -d $LIBPCAP_PREFIX_DIR ]; then
		mkdir $LIBPCAP_PREFIX_DIR
	fi

	if [ ! -d $TCPDUMP_PREFIX_DIR ]; then
		mkdir $TCPDUMP_PREFIX_DIR
	fi

	rm -rf $FTS_TCPDUMP $FTS_LIBPCAP
#	tar -xf ${FTS_LIBPCAP_TARGET}

#	cd $FTS_LIBPCAP
#	./configure --prefix=$LIBPCAP_PREFIX_DIR
#	make || exit 1
#	make install

#	cd ../
#	rm -rf $FTS_LIBPCAP

        tar -xf ${FTS_TCPDUMP_TARGET}
	cd $FTS_TCPDUMP
	./configure --prefix=$TCPDUMP_PREFIX_DIR #--with-system-libpcap=$LIBPCAP_PREFIX_DIR
	make || exit 1
	make install

	cd ../
	rm -rf $FTS_TCPDUMP

	cp ${TCPDUMP_PREFIX_DIR}/bin/tcpdump	${TOPDIR}/bin/
#	cp ${LIBPCAP_PREFIX_DIR}/lib/libpcap.so.1.10.1	${TOPDIR}/lib/
	rm -rf ${TCPDUMP_PREFIX_DIR} ${LIBPCAP_PREFIX_DIR}
}

build_tcpdump

