#!/bin/bash

# smartctl is a command-line utility in Linux used to 
# monitor and analyze the health of hard drives and solid-state drives (SSDs). 
# It is part of the smartmontools package and supports various storage devices 
# such as ATA, SCSI, NVMe, and USB storage devices.
# The smartctl tool uses Self-Monitoring, Analysis, 
# and Reporting Technology (SMART) data to monitor the health and 
# performance of the storage device. The SMART data includes information 
# about the disk's temperature, error rates, and other parameters 
# that can indicate potential issues or failures.

PATH=/bin:/sbin:/usr/bin:/usr/sbin:/sbin:$PATH

TOPDIR=$(realpath ..)
FTS_SMART=smartmontools-7.3
FTS_SMART_TARGET=smartmontools-7.3.tar.gz
SMART_PREFIX_DIR=${TOPDIR}/smart

build_smart() {
	echo "Start build $FTS_SMART ... "

	if [ ! -d $SMART_PREFIX_DIR ]; then
		mkdir $SMART_PREFIX_DIR
	fi

	rm -rf $FTS_SMART
        tar -xf ${FTS_SMART_TARGET}      

	cd $FTS_SMART
	./configure  --prefix=$SMART_PREFIX_DIR
	make || exit 1
	make install

	cd ..
	rm -rf $FTS_SMART

	cp ${SMART_PREFIX_DIR}/sbin/smartctl	${TOPDIR}/bin/

	rm -rf ${SMART_PREFIX_DIR}
}

build_smart

