#!/bin/bash

PATH=/bin:/sbin:/usr/bin:/usr/sbin:/sbin:$PATH

TOPDIR=$(realpath ..)
FTS_SNMP=net-snmp-5.9.4.pre3
FTS_SNMP_TARGET=net-snmp-5.9.4.pre3.tar.gz
SNMP_PREFIX_DIR=${TOPDIR}/snmp

build_snmp() {
	echo "Start build $FTS_SNMP ... "

	if [ ! -d $SNMP_PREFIX_DIR ]; then
		mkdir $SNMP_PREFIX_DIR
	fi

	rm -rf $FTS_SNMP
        tar -xf ${FTS_SNMP_TARGET}      

	cd $FTS_SNMP
	patch -p1 -i ../patch/${FTS_SNMP}.patch
	
	./configure  --prefix=$SNMP_PREFIX_DIR \
		--with-mib-modules='ucd-snmp/diskio' \
		--with-default-snmp-version="3" \
		--with-sys-location="Unknown" \
		--with-sys-contact="fortinet" \
		--with-persistent-directory="/var/net-snmp" \
		--with-logfile="/var/log/snmpd"
	make || exit 1
	make install

	cd ..
	rm -rf $FTS_SNMP

	cp ${SNMP_PREFIX_DIR}/lib/libnetsnmp.so.40.2.1	${TOPDIR}/lib/
	rm -rf ${TOPDIR}/include/net-snmp
	cp -rf ${SNMP_PREFIX_DIR}/include/net-snmp  ${TOPDIR}/include/
	rm -rf ${TOPDIR}/include/net-snmp/agent
	rm -rf ${TOPDIR}/include/net-snmp/data_access
	find ${TOPDIR}/include/net-snmp/system -type f |grep -v "generic.h"| grep -v "linux.h"|grep -v "sysv.h"| xargs rm

	rm -rf ${SNMP_PREFIX_DIR}
}

build_snmp

