#!/bin/bash

# iptables is a command-line utility in Linux used for firewall configuration. 
# It is used to set up and manage rules for packet filtering and 
# network address translation (NAT) in the Linux kernel's netfilter framework.

# For example, to allow incoming traffic on port 80 (HTTP), 
# you can use the following command: 
#        iptables -A INPUT -p tcp --dport 80 -j ACCEPT. 
# This will add a rule to the INPUT chain that allows 
# incoming traffic on port 80.

PATH=/bin:/sbin:/usr/bin:/usr/sbin:/sbin:$PATH

TOPDIR=$(realpath ..)
FTS_IPTABLES=iptables-1.4.21
FTS_IPTABLES_TARGET=iptables-1.4.21.tar.bz2
IPTABLES_PREFIX_DIR=${TOPDIR}/iptables

build_iptables() {
	echo "Start build $FTS_IPTABLES ... "

	if [ ! -d $IPTABLES_PREFIX_DIR ]; then
		mkdir $IPTABLES_PREFIX_DIR
	fi

	rm -rf $FTS_IPTABLES
        tar -xf ${FTS_IPTABLES_TARGET}      

	cd $FTS_IPTABLES
	./configure  --enable-static --disable-shared --prefix=$IPTABLES_PREFIX_DIR
	make || exit 1
	make install

	cd ..
	rm -rf $FTS_IPTABLES

	cp ${IPTABLES_PREFIX_DIR}/sbin/xtables-multi	${TOPDIR}/bin/iptables

	rm -rf ${IPTABLES_PREFIX_DIR}
}

build_iptables

