TOPDIR=$(realpath ..)
OPENSSL=openssl-1.1.1w
OPENSSL_TARGET=openssl-1.1.1w.tar.gz
OPENSSL_SRC_DIR=${TOPDIR}/packages/${OPENSSL}

PREFIX_DIR=${TOPDIR}/openssl

#lib_SAVE_DIR=${TOPDIR}/openssl


build_openssl() {
	echo "Start build $OPENSSL_VER .."

	# if [ -f  ${PREFIX_DIR}/bin/openssl ]; then
	# 	echo "Openssl already exists in ${PREFIX_DIR}"
	# 	return
	# fi
	if [ ! -d ${OPENSSL_SRC_DIR}.tar.gz ]; then
		tar xzf ${OPENSSL_SRC_DIR}.tar.gz
	fi

	cd $OPENSSL_SRC_DIR
    
	./config enable-ssl3 \
    # ./config --prefix=${PREFIX_DIR} -Wl,-rpath,${PREFIX_DIR}/lib \
		enable-ssl3-method \
		enable-ec_nistp_64_gcc_128 \
		enable-weak-ssl-ciphers \
		enable-rc5 \
		enable-md2 \
		enable-deprecated \
		-O3 -pthread -DOPENSSL_THREADS -D_REENTRANT -D_THREAD_SAFE \
		-DOPENSSL_API_COMPAT=0x00908000L
	make || exit 1
	#make install 

    cp libssl.so.1.1 ${lib_SAVE_DIR}/
    cp libcrypto.so.1.1 ${lib_SAVE_DIR}/
    
	cd ..
	rm -rf $OPENSSL_SRC_DIR
	echo "Done."
}


build_openssl
