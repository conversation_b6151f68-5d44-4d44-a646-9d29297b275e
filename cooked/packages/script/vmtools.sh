#!/bin/bash

PATH=/bin:/sbin:/usr/bin:/usr/sbin:/sbin:$PATH

TOPDIR=$(realpath ..)
FTS_VMTOOLS=open-vm-tools-stable-12.4.0
FTS_VMTOOLS_TARGET=open-vm-tools-stable-12.4.0.tar.gz
VMTOOLS_PREFIX_DIR=${TOPDIR}/open-vm-tools
VMTOOLS_VMSVC_PATH=lib/open-vm-tools/plugins/vmsvc

FTS_LIBMSPACK=libmspack-1.11
FTS_LIBMSPACK_TARGET=libmspack-1.11.tar.gz

build_libmspack()
{
	echo "Start build $FTS_LIBMSPACK ... "

	rm -rf $FTS_LIBMSPACK
	tar -xf $FTS_LIBMSPACK_TARGET

	cd $FTS_LIBMSPACK/libmspack
	./autogen.sh
	./configure --prefix=/usr --libdir=/usr/lib64
	make || exit 1
	make install 

	cd -
	rm -rf $FTS_LIBMSPACK

	cp /usr/lib64/libmspack.so.0 ${TOPDIR}/vmtools/lib/
}

build_vmtools() {
	echo "Start build $FTS_VMTOOLS ... "

	if [ ! -d $VMTOOLS_PREFIX_DIR ]; then
		mkdir $VMTOOLS_PREFIX_DIR
	fi

	rm -rf $FTS_VMTOOLS
        tar -xf ${FTS_VMTOOLS_TARGET}      

	cd $FTS_VMTOOLS
	patch -p1 -i ../patch/${FTS_VMTOOLS}.patch

	cd open-vm-tools

	autoreconf -i
	./configure --without-pam --without-x --with-dnet --with-ssl --prefix=$VMTOOLS_PREFIX_DIR
	make || exit 1
	make install

	cd ../..
	rm -rf $FTS_VMTOOLS

	cp ${VMTOOLS_PREFIX_DIR}/lib/libvmtools.so.0.0.0	${TOPDIR}/vmtools/lib/libvmtools.so.0
	cp ${VMTOOLS_PREFIX_DIR}/bin/vmtoolsd	${TOPDIR}/vmtools/bin/
	cp ${VMTOOLS_PREFIX_DIR}/${VMTOOLS_VMSVC_PATH}/libguestInfo.so ${TOPDIR}/vmtools/$VMTOOLS_VMSVC_PATH/
	cp ${VMTOOLS_PREFIX_DIR}/${VMTOOLS_VMSVC_PATH}/libpowerOps.so ${TOPDIR}/vmtools/$VMTOOLS_VMSVC_PATH/
	cp ${VMTOOLS_PREFIX_DIR}/${VMTOOLS_VMSVC_PATH}/libtimeSync.so ${TOPDIR}/vmtools/$VMTOOLS_VMSVC_PATH/
	cp ${VMTOOLS_PREFIX_DIR}/${VMTOOLS_VMSVC_PATH}/libvmbackup.so ${TOPDIR}/vmtools/$VMTOOLS_VMSVC_PATH/

	rm -rf ${VMTOOLS_PREFIX_DIR}
}

build_libmspack
build_vmtools

