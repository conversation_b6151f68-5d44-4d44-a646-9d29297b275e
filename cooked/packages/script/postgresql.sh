#!/bin/bash

PATH=/bin:/sbin:/usr/bin:/usr/sbin:/sbin:$PATH

TOPDIR=$(realpath ..)
FTS_PSQL=postgres-REL_15_10
FTS_PSQL_SRC_DIR=${TOPDIR}/packages/${FTS_PSQL}
FTS_PSQL_TARGET=postgres-REL_15_10.tar.gz
PSQL_PREFIX_DIR=/cooked/$FTS_PSQL
PSQL_SAVE_DIR=${TOPDIR}/postgresql

TLSH_FTNT=tlsh_ftnt
TLSH_FTNT_SRC_DIR=${TOPDIR}/packages/${TLSH_FTNT}
TLSH_FTNT_TARGET=tlsh-ftnt.tar.gz
TLSH_PREFIX_DIR=/cooked/$TLSH_FTNT

TLSH_GIST_FTNT=tlsh_gist-main
TLSH_GIST_FTNT_SRC_DIR=${TOPDIR}/packages/${TLSH_GIST_FTNT}
TLSH_GIST_FTNT_TARGET=tlsh-gist-main-2024-12-03.zip
TLSH_GIST_PREFIX_DIR=/cooked/$TLSH_GIST_FTNT

build_tlsh() {
	cd ${TOPDIR}/packages/

	if [ ! -d $TLSH_PREFIX_DIR ]; then
		mkdir $TLSH_PREFIX_DIR
	fi

	tar -zxvf ${TLSH_FTNT_TARGET}

	cd ${TLSH_FTNT_SRC_DIR}/tlsh
	patch -p5 < ../../patch/tlsh_code_compile_fix.patch

    cd ${TLSH_FTNT_SRC_DIR}
	cp tlsh_wrapper.h tlsh/include/
	mkdir -p install build
	cd build
	cmake -DCMAKE_INSTALL_PREFIX=$TLSH_PREFIX_DIR ../tlsh
	make install

	cd ../..
	rm -rf ${TLSH_FTNT_SRC_DIR}
}

build_tlsh_gist(){
	cd ${TOPDIR}/packages/

	#tar -zxvf ${TLSH_GIST_FTNT_TARGET}
    unzip ${TLSH_GIST_FTNT_TARGET}

	cd ${TLSH_GIST_FTNT_SRC_DIR}

	patch -p1 < ../patch/tlsh_gist_compile_fix.patch

	make
	mkdir install
	make install DESTDIR=$TLSH_GIST_PREFIX_DIR

	cp ./tlsh_gist.so ${PSQL_SAVE_DIR}/lib

	cd ..

	rm -rf ${TLSH_GIST_FTNT_SRC_DIR}
}

build_psql() {
	echo "Start build $FTS_PSQL ... "

	if [ ! -d $PSQL_PREFIX_DIR ]; then
		mkdir $PSQL_PREFIX_DIR
	fi

	if [ -d $FTS_PSQL ]; then
		rm -rf $FTS_PSQL
	fi
	tar -xf ${FTS_PSQL_TARGET}

	cd $FTS_PSQL
	./configure  --prefix=$PSQL_PREFIX_DIR --with-uuid=e2fs
	make || exit 1
	make install
	cd $FTS_PSQL_SRC_DIR/contrib/uuid-ossp
	make || exit 1
	make install|| exit 1
	cd $FTS_PSQL_SRC_DIR/contrib/pg_trgm
	make || exit 1
	make install|| exit 1

	cd ..

	if [ ! -d $PSQL_SAVE_DIR/bin ]; then
		mkdir -p $PSQL_SAVE_DIR/bin
	fi

	if [ ! -d $PSQL_SAVE_DIR/lib ]; then
		mkdir -p $PSQL_SAVE_DIR/lib
	fi

	cp ${PSQL_PREFIX_DIR}/bin/postgres ${PSQL_SAVE_DIR}/bin/
	cp ${PSQL_PREFIX_DIR}/bin/psql ${PSQL_SAVE_DIR}/bin/
	cp ${PSQL_PREFIX_DIR}/bin/pg_ctl ${PSQL_SAVE_DIR}/bin/
	cp ${PSQL_PREFIX_DIR}/bin/initdb ${PSQL_SAVE_DIR}/bin/
	cp ${PSQL_PREFIX_DIR}/bin/createdb ${PSQL_SAVE_DIR}/bin/
	cp ${PSQL_PREFIX_DIR}/bin/pg_config ${PSQL_SAVE_DIR}/bin/
	cp ${PSQL_PREFIX_DIR}/bin/pg_dump ${PSQL_SAVE_DIR}/bin/

	cp ${PSQL_PREFIX_DIR}/lib/libpq.so.5 ${PSQL_SAVE_DIR}/lib/
	cp ${PSQL_PREFIX_DIR}/lib/dict_snowball.so ${PSQL_SAVE_DIR}/lib
	cp ${PSQL_PREFIX_DIR}/lib/plpgsql.so ${PSQL_SAVE_DIR}/lib
	cp ${PSQL_PREFIX_DIR}/lib/uuid-ossp.so ${PSQL_SAVE_DIR}/lib
	cp ${PSQL_PREFIX_DIR}/lib/pg_trgm.so ${PSQL_SAVE_DIR}/lib

	cp -rf ${PSQL_PREFIX_DIR}/share ${PSQL_SAVE_DIR}/

	#rm -rf $FTS_PSQL
	#rm -rf ${PSQL_PREFIX_DIR}
}

build_psql
build_tlsh
build_tlsh_gist
