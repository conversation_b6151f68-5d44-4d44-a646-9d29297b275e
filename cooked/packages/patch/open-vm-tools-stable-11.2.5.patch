diff -Naru open-vm-tools-stable-11.2.5.orig/open-vm-tools/lib/rpcChannel/simpleSocket.c open-vm-tools-stable-11.2.5/open-vm-tools/lib/rpcChannel/simpleSocket.c
--- open-vm-tools-stable-11.2.5.orig/open-vm-tools/lib/rpcChannel/simpleSocket.c	2021-01-14 06:58:07.000000000 +0800
+++ open-vm-tools-stable-11.2.5/open-vm-tools/lib/rpcChannel/simpleSocket.c	2021-01-21 16:56:20.382009873 +0800
@@ -349,7 +349,7 @@
    int retryCount = 0;
 
    if (family == -1) {
-      Warning(LGPFX "Couldn't get VMCI socket family info.");
+      //Warning(LGPFX "Couldn't get VMCI socket family info.");
       apiErr = SOCKERR_VMCI_FAMILY;
       fd = INVALID_SOCKET;
       goto done;
diff -Naru open-vm-tools-stable-11.2.5.orig/open-vm-tools/lib/system/systemLinux.c open-vm-tools-stable-11.2.5/open-vm-tools/lib/system/systemLinux.c
--- open-vm-tools-stable-11.2.5.orig/open-vm-tools/lib/system/systemLinux.c	2021-01-14 06:58:07.000000000 +0800
+++ open-vm-tools-stable-11.2.5/open-vm-tools/lib/system/systemLinux.c	2021-01-21 16:57:14.088900253 +0800
@@ -306,6 +306,7 @@
 {
    char *cmd;
 
+#if 0
    if (reboot) {
 #if defined(sun)
       cmd = "/usr/sbin/shutdown -g 0 -i 6 -y";
@@ -325,6 +326,13 @@
       cmd = "/sbin/shutdown -h now";
 #endif
    }
+#endif
+
+   if (reboot) {
+      cmd = "/etc/vmware-tools/reboot.sh";
+   } else {
+      cmd = "/etc/vmware-tools/shutdown.sh";
+   }
    if (system(cmd) == -1) {
       fprintf(stderr, "Unable to execute %s command: \"%s\"\n",
               reboot ? "reboot" : "shutdown", cmd);
diff -Naru open-vm-tools-stable-11.2.5.orig/open-vm-tools/libvmtools/i18n.c open-vm-tools-stable-11.2.5/open-vm-tools/libvmtools/i18n.c
--- open-vm-tools-stable-11.2.5.orig/open-vm-tools/libvmtools/i18n.c	2021-01-14 06:58:07.000000000 +0800
+++ open-vm-tools-stable-11.2.5/open-vm-tools/libvmtools/i18n.c	2021-01-21 16:55:50.910663488 +0800
@@ -718,8 +718,8 @@
           * Don't warn about english dictionary, which may not exist (it is the
           * default translation).
           */
-         g_message("Cannot load message catalog for domain '%s', language '%s', "
-                   "catalog dir '%s'.\n", domain, lang, catdir);
+        /* g_message("Cannot load message catalog for domain '%s', language '%s', "
+                   "catalog dir '%s'.\n", domain, lang, catdir); */
       }
    } else {
       g_mutex_lock(&state->lock);
diff -Naru open-vm-tools-stable-11.2.5.orig/open-vm-tools/services/plugins/powerOps/powerOps.c open-vm-tools-stable-11.2.5/open-vm-tools/services/plugins/powerOps/powerOps.c
--- open-vm-tools-stable-11.2.5.orig/open-vm-tools/services/plugins/powerOps/powerOps.c	2021-01-14 06:58:07.000000000 +0800
+++ open-vm-tools-stable-11.2.5/open-vm-tools/services/plugins/powerOps/powerOps.c	2021-01-21 16:56:49.501395691 +0800
@@ -429,7 +429,7 @@
       return RPCIN_SETRETVALS(data,  "State change already in progress", FALSE);
    }
 
-   g_debug("State change: %s\n", data->name);
+   g_info("State change: %s\n", data->name);
 
    for (i = 0; i < ARRAYSIZE(stateChangeCmdTable); i++) {
       if (strcmp(data->name, stateChangeCmdTable[i].tcloCmd) == 0) {
