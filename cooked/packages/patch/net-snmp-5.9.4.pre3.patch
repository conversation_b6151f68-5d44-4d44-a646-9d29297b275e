diff --git a/agent/mibgroup/if-mib/data_access/interface_linux.c b/agent/mibgroup/if-mib/data_access/interface_linux.c
index 0b8152b..9ae1cac 100644
--- a/agent/mibgroup/if-mib/data_access/interface_linux.c
+++ b/agent/mibgroup/if-mib/data_access/interface_linux.c
@@ -31,7 +31,7 @@ static struct pci_access *pci_access;
 /* Avoid letting libpci call exit(1) when no PCI bus is available. */
 static int do_longjmp =0;
 static jmp_buf err_buf;
-PCI_NONRET static void
+static void
 netsnmp_pci_error(char *msg, ...)
 {
     va_list args;
