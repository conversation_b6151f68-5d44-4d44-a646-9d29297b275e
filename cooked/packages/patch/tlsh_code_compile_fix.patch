diff --git a/home/<USER>/tlsh_code/tlsh/CMakeLists.txt b/CMakeLists.txt
index cd1092c..8ca247e 100644
--- a/home/<USER>/tlsh_code/tlsh/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -1,3 +1,4 @@
+cmake_minimum_required(VERSION 3.22)
 # TLSH is provided for use under two licenses: Apache OR BSD.
 # Users may opt to use either license depending on the license
 # restictions of the systems with which they plan to integrate
@@ -117,6 +118,7 @@ set(ALL_FILES
 # Target
 ################################################################################
 add_library(${PROJECT_NAME} STATIC ${ALL_FILES})
+add_library(${PROJECT_NAME}_shared SHARED ${ALL_FILES})
 
 list(APPEND COMPILE_EXTRA_DEFS BUCKETS_128)
 
@@ -135,16 +137,36 @@ if(MSVC)
     list(APPEND COMPILE_EXTRA_OPTS /wd4018 /wd4244 /wd4267 /wd4333 /wd4554 /wd4996 )
 endif()
 
-process_default_target()
+#process_default_target()
 
 set_target_properties(${PROJECT_NAME} PROPERTIES FOLDER "share")
 
+target_compile_definitions(${PROJECT_NAME} PRIVATE ${COMPILE_EXTRA_DEFS})
+target_compile_definitions(${PROJECT_NAME}_shared PRIVATE ${COMPILE_EXTRA_DEFS})
+
 ################################################################################
 # Include directories
 ################################################################################
 target_include_directories(${PROJECT_NAME} PRIVATE
     "${CMAKE_SOURCE_DIR}/share/include"
+    "${CMAKE_CURRENT_BINARY_DIR}/include"
+    "${CMAKE_CURRENT_SOURCE_DIR}/include"
+)
 
+target_include_directories(${PROJECT_NAME}_shared PRIVATE
+    "${CMAKE_SOURCE_DIR}/share/include"
     "${CMAKE_CURRENT_BINARY_DIR}/include"
     "${CMAKE_CURRENT_SOURCE_DIR}/include"
-)
\ No newline at end of file
+)
+
+################################################################################
+# Install
+################################################################################
+install(TARGETS ${PROJECT_NAME} ${PROJECT_NAME}_shared
+    RUNTIME DESTINATION bin
+    LIBRARY DESTINATION lib
+    ARCHIVE DESTINATION lib
+)
+
+install(FILES ${HEADER_FILES} DESTINATION include/tlsh)
+install(FILES ${CMAKE_CURRENT_BINARY_DIR}/include/tlsh_version.h DESTINATION include/tlsh)
\ No newline at end of file
