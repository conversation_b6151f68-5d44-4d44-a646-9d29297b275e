diff -Naru openssl-OpenSSL_1_1_1t.orig/Configure openssl-OpenSSL_1_1_1t/Configure
--- openssl-OpenSSL_1_1_1t.orig/Configure	2023-02-07 05:37:05.000000000 -0800
+++ openssl-OpenSSL_1_1_1t/Configure	2023-04-17 09:15:24.097725613 -0700
@@ -318,7 +318,7 @@
 my $default_ranlib;
 
 # Top level directories to build
-$config{dirs} = [ "crypto", "ssl", "engines", "apps", "test", "util", "tools", "fuzz" ];
+$config{dirs} = [ "crypto", "ssl", "engines" ];
 # crypto/ subdirectories to build
 $config{sdirs} = [
     "objects",
diff -Naru openssl-OpenSSL_1_1_1t.orig/crypto/async/arch/async_posix.c openssl-OpenSSL_1_1_1t/crypto/async/arch/async_posix.c
--- openssl-OpenSSL_1_1_1t.orig/crypto/async/arch/async_posix.c	2023-02-07 05:37:05.000000000 -0800
+++ openssl-OpenSSL_1_1_1t/crypto/async/arch/async_posix.c	2023-04-17 09:15:24.185725079 -0700
@@ -15,7 +15,12 @@
 # include <stddef.h>
 # include <unistd.h>
 
-#define STACKSIZE       32768
+static size_t STACKSIZE = 32768;
+
+void async_set_stack_size(size_t size)
+{
+	STACKSIZE = size;
+}
 
 int ASYNC_is_capable(void)
 {
diff -Naru openssl-OpenSSL_1_1_1t.orig/crypto/engine/eng_ctrl.c openssl-OpenSSL_1_1_1t/crypto/engine/eng_ctrl.c
--- openssl-OpenSSL_1_1_1t.orig/crypto/engine/eng_ctrl.c	2023-02-07 05:37:05.000000000 -0800
+++ openssl-OpenSSL_1_1_1t/crypto/engine/eng_ctrl.c	2023-04-17 09:15:24.285724473 -0700
@@ -122,6 +122,14 @@
     return -1;
 }
 
+int qat_polling(ENGINE *e, long i, void *p, void (*f) (void))
+{
+    if(e && e->ctrl)
+        return e->ctrl(e, 201, i, p, f); /* 201 strands for POLL cmd */
+    else
+        return 0;
+}
+
 int ENGINE_ctrl(ENGINE *e, int cmd, long i, void *p, void (*f) (void))
 {
     int ctrl_exists, ref_exists;

diff -Naru openssl-OpenSSL_1_1_1t.orig/include/internal/sockets.h openssl-OpenSSL_1_1_1t/include/internal/sockets.h
--- openssl-OpenSSL_1_1_1t.orig/include/internal/sockets.h	2023-02-07 05:37:05.000000000 -0800
+++ openssl-OpenSSL_1_1_1t/include/internal/sockets.h	2023-04-17 09:15:24.621722436 -0700
@@ -147,6 +147,14 @@
 #  define closesocket(s)              close(s)
 #  define readsocket(s,b,n)           read((s),(b),(n))
 #  define writesocket(s,b,n)          write((s),(char *)(b),(n))
+# elif defined(_DPDK_USE_SSL_)
+extern int fts_close(int sock_fd);
+extern ssize_t fts_read(int sock_fd, void *buf, size_t count);
+extern ssize_t fts_write(int sock_fd, const void *buf, size_t count);
+#  define ioctlsocket(a,b,c)      ioctl(a,b,c)
+#  define closesocket(s)          fts_close(s)
+#  define readsocket(s,b,n)       fts_read((s),(b),(n))
+#  define writesocket(s,b,n)      fts_write((s),(b),(n))
 # else
 #  define ioctlsocket(a,b,c)      ioctl(a,b,c)
 #  define closesocket(s)          close(s)

diff -Naru openssl-OpenSSL_1_1_1t.orig/include/openssl/engine.h openssl-OpenSSL_1_1_1t/include/openssl/engine.h
--- openssl-OpenSSL_1_1_1t.orig/include/openssl/engine.h	2023-02-07 05:37:05.000000000 -0800
+++ openssl-OpenSSL_1_1_1t/include/openssl/engine.h	2023-04-17 09:15:24.637722340 -0700
@@ -411,6 +411,7 @@
  * references in such situations.
  */
 int ENGINE_ctrl(ENGINE *e, int cmd, long i, void *p, void (*f) (void));
+int qat_polling(ENGINE *e, long i, void *p, void (*f) (void));
 
 /*
  * This function tests if an ENGINE-specific command is usable as a
diff -Naru openssl-OpenSSL_1_1_1t.orig/crypto/rsa/rsa_ossl.c openssl-OpenSSL_1_1_1t/crypto/rsa/rsa_ossl.c
--- openssl-OpenSSL_1_1_1t.orig/crypto/rsa/rsa_ossl.c	2023-02-07 05:37:05.000000000 -0800
+++ openssl-OpenSSL_1_1_1t/crypto/rsa/rsa_ossl.c	2023-04-17 09:15:24.357724037 -0700
@@ -465,20 +465,16 @@
         BN_free(d);
     }
 
-    if (blinding) {
+    if (blinding)
         /*
-         * ossl_bn_rsa_do_unblind() combines blinding inversion and
-         * 0-padded BN BE serialization
+         * due to the performance drop by fix CVE-2022-4304, revert to old code
          */
-        j = ossl_bn_rsa_do_unblind(ret, blinding, unblind, rsa->n, ctx,
-                                   buf, num);
-        if (j == 0)
+        if (!rsa_blinding_invert(blinding, ret, unblind, ctx))
             goto err;
-    } else {
-        j = BN_bn2binpad(ret, buf, num);
-        if (j < 0)
+
+    j = BN_bn2binpad(ret, buf, num);
+    if (j < 0)
             goto err;
-    }
 
     switch (padding) {
     case RSA_PKCS1_PADDING:
diff -Naru openssl-OpenSSL_1_1_1t.orig/ssl/ssl_lib.c openssl-OpenSSL_1_1_1t/ssl/ssl_lib.c
--- openssl-OpenSSL_1_1_1t.orig/ssl/ssl_lib.c	2023-02-07 05:37:05.000000000 -0800
+++ openssl-OpenSSL_1_1_1t/ssl/ssl_lib.c	2023-08-23 14:10:40.394300902 -0700
@@ -1768,6 +1768,7 @@
      */
     ossl_statem_check_finish_init(s, 0);
 
+    /*
     if ((s->mode & SSL_MODE_ASYNC) && ASYNC_get_current_job() == NULL) {
         struct ssl_async_args args;
         int ret;
@@ -1783,7 +1784,9 @@
         return ret;
     } else {
         return s->method->ssl_read(s, buf, num, readbytes);
-    }
+    }*/
+
+    return s->method->ssl_read(s, buf, num, readbytes);
 }
 
 int SSL_read(SSL *s, void *buf, int num)
@@ -1959,6 +1962,7 @@
     /* If we are a client and haven't sent the Finished we better do that */
     ossl_statem_check_finish_init(s, 1);
 
+    /*
     if ((s->mode & SSL_MODE_ASYNC) && ASYNC_get_current_job() == NULL) {
         int ret;
         struct ssl_async_args args;
@@ -1974,7 +1978,9 @@
         return ret;
     } else {
         return s->method->ssl_write(s, buf, num, written);
-    }
+    }*/
+
+    return s->method->ssl_write(s, buf, num, written);
 }
 
 int SSL_write(SSL *s, const void *buf, int num)
@@ -2095,6 +2101,7 @@
     }
 
     if (!SSL_in_init(s)) {
+	/*
         if ((s->mode & SSL_MODE_ASYNC) && ASYNC_get_current_job() == NULL) {
             struct ssl_async_args args;
 
@@ -2106,7 +2113,9 @@
             return ssl_start_async_job(s, &args, ssl_io_intern);
         } else {
             return s->method->ssl_shutdown(s);
-        }
+        }*/
+
+        return s->method->ssl_shutdown(s);
     } else {
         SSLerr(SSL_F_SSL_SHUTDOWN, SSL_R_SHUTDOWN_WHILE_IN_INIT);
         return -1;

