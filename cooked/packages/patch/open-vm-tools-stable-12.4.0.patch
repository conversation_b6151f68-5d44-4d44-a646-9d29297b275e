diff --git a/open-vm-tools/lib/rpcChannel/simpleSocket.c b/open-vm-tools/lib/rpcChannel/simpleSocket.c
index 977a3ac2..51e9e0ce 100644
--- a/open-vm-tools/lib/rpcChannel/simpleSocket.c
+++ b/open-vm-tools/lib/rpcChannel/simpleSocket.c
@@ -354,7 +354,7 @@ Socket_ConnectVMCI(unsigned int cid,                  // IN
    int retryCountNoBufs = 0;
 
    if (family == -1) {
-      Warning(LGPFX "Couldn't get VMCI socket family info.");
+      //Warning(LGPFX "Couldn't get VMCI socket family info.");
       apiErr = SOCKERR_VMCI_FAMILY;
       fd = INVALID_SOCKET;
       goto done;
diff --git a/open-vm-tools/lib/system/systemLinux.c b/open-vm-tools/lib/system/systemLinux.c
index a688ab25..32248c9f 100644
--- a/open-vm-tools/lib/system/systemLinux.c
+++ b/open-vm-tools/lib/system/systemLinux.c
@@ -306,6 +306,7 @@ System_Shutdown(Bool reboot)  // IN: "reboot or shutdown" flag
 {
    char *cmd;
 
+#if 0
    if (reboot) {
 #if defined(sun)
       cmd = "/usr/sbin/shutdown -g 0 -i 6 -y";
@@ -325,6 +326,13 @@ System_Shutdown(Bool reboot)  // IN: "reboot or shutdown" flag
       cmd = "/sbin/shutdown -h now";
 #endif
    }
+#endif
+   if (reboot) {
+      cmd = "/etc/vmware-tools/reboot.sh";
+    } else {
+      cmd = "/etc/vmware-tools/shutdown.sh";
+    }
+
    if (system(cmd) == -1) {
       fprintf(stderr, "Unable to execute %s command: \"%s\"\n",
               reboot ? "reboot" : "shutdown", cmd);
diff --git a/open-vm-tools/libvmtools/i18n.c b/open-vm-tools/libvmtools/i18n.c
index 3085f72d..ebe38f9a 100644
--- a/open-vm-tools/libvmtools/i18n.c
+++ b/open-vm-tools/libvmtools/i18n.c
@@ -718,8 +718,8 @@ VMTools_BindTextDomain(const char *domain,
           * Don't warn about english dictionary, which may not exist (it is the
           * default translation).
           */
-         g_message("Cannot load message catalog for domain '%s', language '%s', "
-                   "catalog dir '%s'.\n", domain, lang, catdir);
+         /*g_message("Cannot load message catalog for domain '%s', language '%s', "
+                   "catalog dir '%s'.\n", domain, lang, catdir);*/
       }
    } else {
       g_mutex_lock(&state->lock);
diff --git a/open-vm-tools/services/plugins/powerOps/powerOps.c b/open-vm-tools/services/plugins/powerOps/powerOps.c
index 5fd9aaba..204e8567 100644
--- a/open-vm-tools/services/plugins/powerOps/powerOps.c
+++ b/open-vm-tools/services/plugins/powerOps/powerOps.c
@@ -434,7 +434,7 @@ PowerOpsStateChange(RpcInData *data)
       return RPCIN_SETRETVALS(data, "State change already in progress", FALSE);
    }
 
-   g_debug("State change: %s\n", data->name);
+   g_info("State change: %s\n", data->name);
 
    for (i = 0; i < ARRAYSIZE(stateChangeCmdTable); i++) {
       if (strcmp(data->name, stateChangeCmdTable[i].tcloCmd) == 0) {
