diff -Naru openssh-portable-V_9_8_P1.orig/auth2-pubkey.c openssh-portable-V_9_8_P1/auth2-pubkey.c
--- openssh-portable-V_9_8_P1.orig/auth2-pubkey.c	2024-06-30 21:36:28.********* -0700
+++ openssh-portable-V_9_8_P1/auth2-pubkey.c	2025-08-12 16:08:39.632420098 -0700
@@ -300,6 +300,20 @@
 	}
 	debug2_f("authenticated %d pkalg %s", authenticated, pkalg);
 
+// #ifdef HAVE_FDT
+// #include "fdt_autoconf.h"
+
+// 	if (authenticated == 1 && authctxt->user && strcmp(authctxt->user, "admin")) {
+// 		authenticated = 0;
+// 	}
+// #if defined(CONFIG_SYSTEM_FDT_VM_AWS) || defined(CONFIG_SYSTEM_FDT_VM_AWS_BYOL)
+// 	if (authenticated && authctxt->user) {
+//         debug3_f("el_sshd_setlogin(%s), pid:%d", authctxt->user, getpid());
+// 		el_sshd_setlogin(authctxt->user);
+// 	}
+// #endif
+// #endif
+
 	sshbuf_free(b);
 	sshauthopt_free(authopts);
 	sshkey_free(key);
diff -Naru openssh-portable-V_9_8_P1.orig/auth.c openssh-portable-V_9_8_P1/auth.c
--- openssh-portable-V_9_8_P1.orig/auth.c	2024-06-30 21:36:28.********* -0700
+++ openssh-portable-V_9_8_P1/auth.c	2025-08-12 16:08:39.566419853 -0700
@@ -484,7 +484,11 @@
 	aix_setauthdb(user);
 #endif
 
+#ifdef HAVE_FDT
+	pw = el_getpwnam(user);
+#else
 	pw = getpwnam(user);
+#endif
 
 #if defined(_AIX) && defined(HAVE_SETAUTHDB)
 	aix_restoreauthdb();
diff -Naru openssh-portable-V_9_8_P1.orig/auth-passwd.c openssh-portable-V_9_8_P1/auth-passwd.c
--- openssh-portable-V_9_8_P1.orig/auth-passwd.c	2024-06-30 21:36:28.********* -0700
+++ openssh-portable-V_9_8_P1/auth-passwd.c	2025-08-12 16:08:39.565419850 -0700
@@ -55,6 +55,9 @@
 #include "hostfile.h"
 #include "auth.h"
 #include "auth-options.h"
+#ifdef HAVE_FDT
+#include "fdt_auth.h"
+#endif
 
 extern struct sshbuf *loginmsg;
 extern ServerOptions options;
@@ -122,9 +125,27 @@
 			authctxt->force_pwchange = 1;
 	}
 #endif
+#ifdef HAVE_FDT
+	if (authctxt->user) {
+		result = cli_admin_auth(authctxt->user, password,
+				LOGIN_FROM_SSH,
+				ssh_remote_ipaddr(ssh));
+		// if (result) {
+		// 	el_sshd_setlogin(authctxt->user);
+		// }
+		debug("Authentication user '%s' passwd '%s', retn: %d, ok %d\n",
+						authctxt->user,
+						password,
+						result,
+						result && ok);
+	} else {
+		result = 0;	
+	}
+#else
 	result = sys_auth_passwd(ssh, password);
 	if (authctxt->force_pwchange)
 		auth_restrict_session(ssh);
+#endif
 	return (result && ok);
 }
 
diff -Naru openssh-portable-V_9_8_P1.orig/config.h openssh-portable-V_9_8_P1/config.h
--- openssh-portable-V_9_8_P1.orig/config.h	1969-12-31 16:00:00.********* -0800
+++ openssh-portable-V_9_8_P1/config.h	2025-08-12 16:08:39.577419894 -0700
@@ -0,0 +1,1903 @@
+/* config.h.  Generated from config.h.in by configure.  */
+/* config.h.in.  Generated from configure.ac by autoheader.  */
+
+/* Define if building universal (internal helper macro) */
+/* #undef AC_APPLE_UNIVERSAL_BUILD */
+
+/* Define if you have a getaddrinfo that fails for the all-zeros IPv6 address
+   */
+/* #undef AIX_GETNAMEINFO_HACK */
+
+/* Define if your AIX loginfailed() function takes 4 arguments (AIX >= 5.2) */
+/* #undef AIX_LOGINFAILED_4ARG */
+
+/* System only supports IPv4 audit records */
+/* #undef AU_IPv4 */
+
+/* Define if your resolver libs need this for getrrsetbyname */
+/* #undef BIND_8_COMPAT */
+
+/* The system has incomplete BSM API */
+/* #undef BROKEN_BSM_API */
+
+/* Define if cmsg_type is not passed correctly */
+/* #undef BROKEN_CMSG_TYPE */
+
+/* getaddrinfo is broken (if present) */
+/* #undef BROKEN_GETADDRINFO */
+
+/* getgroups(0,NULL) will return -1 */
+/* #undef BROKEN_GETGROUPS */
+
+/* FreeBSD glob does not do what we need */
+/* #undef BROKEN_GLOB */
+
+/* Define if you system's inet_ntoa is busted (e.g. Irix gcc issue) */
+/* #undef BROKEN_INET_NTOA */
+
+/* Define if your struct dirent expects you to allocate extra space for d_name
+   */
+/* #undef BROKEN_ONE_BYTE_DIRENT_D_NAME */
+
+/* Can't do comparisons on readv */
+/* #undef BROKEN_READV_COMPARISON */
+
+/* NetBSD read function is sometimes redirected, breaking atomicio comparisons
+   against it */
+/* #undef BROKEN_READ_COMPARISON */
+
+/* realpath does not work with nonexistent files */
+#define BROKEN_REALPATH 1
+
+/* Needed for NeXT */
+/* #undef BROKEN_SAVED_UIDS */
+
+/* Define if your setregid() is broken */
+/* #undef BROKEN_SETREGID */
+
+/* Define if your setresgid() is broken */
+/* #undef BROKEN_SETRESGID */
+
+/* Define if your setresuid() is broken */
+/* #undef BROKEN_SETRESUID */
+
+/* Define if your setreuid() is broken */
+/* #undef BROKEN_SETREUID */
+
+/* LynxOS has broken setvbuf() implementation */
+/* #undef BROKEN_SETVBUF */
+
+/* QNX shadow support is broken */
+/* #undef BROKEN_SHADOW_EXPIRE */
+
+/* Define if your snprintf is busted */
+/* #undef BROKEN_SNPRINTF */
+
+/* strndup broken, see APAR IY61211 */
+/* #undef BROKEN_STRNDUP */
+
+/* strnlen broken, see APAR IY62551 */
+/* #undef BROKEN_STRNLEN */
+
+/* strnvis detected broken */
+#define BROKEN_STRNVIS 1
+
+/* tcgetattr with ICANON may hang */
+/* #undef BROKEN_TCGETATTR_ICANON */
+
+/* updwtmpx is broken (if present) */
+/* #undef BROKEN_UPDWTMPX */
+
+/* Define if you have BSD auth support */
+/* #undef BSD_AUTH */
+
+/* Define if you want to specify the path to your lastlog file */
+/* #undef CONF_LASTLOG_FILE */
+
+/* Define if you want to specify the path to your utmp file */
+/* #undef CONF_UTMP_FILE */
+
+/* Define if you want to specify the path to your wtmpx file */
+/* #undef CONF_WTMPX_FILE */
+
+/* Define if you want to specify the path to your wtmp file */
+/* #undef CONF_WTMP_FILE */
+
+/* Define if your platform needs to skip post auth file descriptor passing */
+/* #undef DISABLE_FD_PASSING */
+
+/* Define if you don't want to use lastlog */
+/* #undef DISABLE_LASTLOG */
+
+/* Define if you don't want to use your system's login() call */
+/* #undef DISABLE_LOGIN */
+
+/* Define if you don't want to use pututline() etc. to write [uw]tmp */
+/* #undef DISABLE_PUTUTLINE */
+
+/* Define if you don't want to use pututxline() etc. to write [uw]tmpx */
+/* #undef DISABLE_PUTUTXLINE */
+
+/* Define if you want to disable shadow passwords */
+/* #undef DISABLE_SHADOW */
+
+/* Define if you don't want to use utmp */
+/* #undef DISABLE_UTMP */
+
+/* Define if you don't want to use utmpx */
+/* #undef DISABLE_UTMPX */
+
+/* Define if you don't want to use wtmp */
+/* #undef DISABLE_WTMP */
+
+/* Define if you don't want to use wtmpx */
+#define DISABLE_WTMPX 1
+
+/* Enable for PKCS#11 support */
+#define ENABLE_PKCS11 /**/
+
+/* define if fflush(NULL) does not work */
+/* #undef FFLUSH_NULL_BUG */
+
+/* File names may not contain backslash characters */
+/* #undef FILESYSTEM_NO_BACKSLASH */
+
+/* fsid_t has member val */
+/* #undef FSID_HAS_VAL */
+
+/* fsid_t has member __val */
+/* #undef FSID_HAS___VAL */
+
+/* getpgrp takes one arg */
+#define GETPGRP_VOID 1
+
+/* Conflicting defs for getspnam */
+/* #undef GETSPNAM_CONFLICTING_DEFS */
+
+/* Define if your system glob() function has the GLOB_ALTDIRFUNC extension */
+#define GLOB_HAS_ALTDIRFUNC 1
+
+/* Define if your system glob() function has gl_matchc options in glob_t */
+/* #undef GLOB_HAS_GL_MATCHC */
+
+/* Define if your system glob() function has gl_statv options in glob_t */
+/* #undef GLOB_HAS_GL_STATV */
+
+/* Define this if you want GSSAPI support in the version 2 protocol */
+/* #undef GSSAPI */
+
+/* Define if you want to use shadow password expire field */
+#define HAS_SHADOW_EXPIRE 1
+
+/* Define if your system uses access rights style file descriptor passing */
+/* #undef HAVE_ACCRIGHTS_IN_MSGHDR */
+
+/* Define if you have ut_addr in utmp.h */
+#define HAVE_ADDR_IN_UTMP 1
+
+/* Define if you have ut_addr in utmpx.h */
+#define HAVE_ADDR_IN_UTMPX 1
+
+/* Define if you have ut_addr_v6 in utmp.h */
+#define HAVE_ADDR_V6_IN_UTMP 1
+
+/* Define if you have ut_addr_v6 in utmpx.h */
+#define HAVE_ADDR_V6_IN_UTMPX 1
+
+/* Define to 1 if you have the `arc4random' function. */
+/* #undef HAVE_ARC4RANDOM */
+
+/* Define to 1 if you have the `arc4random_buf' function. */
+/* #undef HAVE_ARC4RANDOM_BUF */
+
+/* Define to 1 if you have the `arc4random_stir' function. */
+/* #undef HAVE_ARC4RANDOM_STIR */
+
+/* Define to 1 if you have the `arc4random_uniform' function. */
+/* #undef HAVE_ARC4RANDOM_UNIFORM */
+
+/* Define to 1 if you have the `asprintf' function. */
+#define HAVE_ASPRINTF 1
+
+/* OpenBSD's gcc has bounded */
+/* #undef HAVE_ATTRIBUTE__BOUNDED__ */
+
+/* Have attribute nonnull */
+#define HAVE_ATTRIBUTE__NONNULL__ 1
+
+/* OpenBSD's gcc has sentinel */
+/* #undef HAVE_ATTRIBUTE__SENTINEL__ */
+
+/* Define to 1 if you have the `aug_get_machine' function. */
+/* #undef HAVE_AUG_GET_MACHINE */
+
+/* Define to 1 if you have the `b64_ntop' function. */
+/* #undef HAVE_B64_NTOP */
+
+/* Define to 1 if you have the `b64_pton' function. */
+/* #undef HAVE_B64_PTON */
+
+/* Define if you have the basename function. */
+#define HAVE_BASENAME 1
+
+/* Define to 1 if you have the `bcopy' function. */
+#define HAVE_BCOPY 1
+
+/* Define to 1 if you have the `bcrypt_pbkdf' function. */
+/* #undef HAVE_BCRYPT_PBKDF */
+
+/* Define to 1 if you have the `bindresvport_sa' function. */
+/* #undef HAVE_BINDRESVPORT_SA */
+
+/* Define to 1 if you have the `blf_enc' function. */
+/* #undef HAVE_BLF_ENC */
+
+/* Define to 1 if you have the <blf.h> header file. */
+/* #undef HAVE_BLF_H */
+
+/* Define to 1 if you have the `Blowfish_expand0state' function. */
+/* #undef HAVE_BLOWFISH_EXPAND0STATE */
+
+/* Define to 1 if you have the `Blowfish_expandstate' function. */
+/* #undef HAVE_BLOWFISH_EXPANDSTATE */
+
+/* Define to 1 if you have the `Blowfish_initstate' function. */
+/* #undef HAVE_BLOWFISH_INITSTATE */
+
+/* Define to 1 if you have the `Blowfish_stream2word' function. */
+/* #undef HAVE_BLOWFISH_STREAM2WORD */
+
+/* Define to 1 if you have the `BN_is_prime_ex' function. */
+#define HAVE_BN_IS_PRIME_EX 1
+
+/* Define to 1 if you have the <bsd/libutil.h> header file. */
+/* #undef HAVE_BSD_LIBUTIL_H */
+
+/* Define to 1 if you have the <bsm/audit.h> header file. */
+/* #undef HAVE_BSM_AUDIT_H */
+
+/* Define to 1 if you have the <bstring.h> header file. */
+/* #undef HAVE_BSTRING_H */
+
+/* Define to 1 if you have the `bzero' function. */
+#define HAVE_BZERO 1
+
+/* calloc(0, x) returns NULL */
+#define HAVE_CALLOC 1
+
+/* Define to 1 if you have the `cap_rights_limit' function. */
+/* #undef HAVE_CAP_RIGHTS_LIMIT */
+
+/* Define to 1 if you have the `clock' function. */
+#define HAVE_CLOCK 1
+
+/* Have clock_gettime */
+#define HAVE_CLOCK_GETTIME 1
+
+/* define if you have clock_t data type */
+#define HAVE_CLOCK_T 1
+
+/* Define to 1 if you have the `closefrom' function. */
+/* #undef HAVE_CLOSEFROM */
+
+/* Define if gai_strerror() returns const char * */
+#define HAVE_CONST_GAI_STRERROR_PROTO 1
+
+/* Define if your system uses ancillary data style file descriptor passing */
+#define HAVE_CONTROL_IN_MSGHDR 1
+
+/* Define to 1 if you have the `crypt' function. */
+#define HAVE_CRYPT 1
+
+/* Define to 1 if you have the <crypto/sha2.h> header file. */
+/* #undef HAVE_CRYPTO_SHA2_H */
+
+/* Define to 1 if you have the <crypt.h> header file. */
+#define HAVE_CRYPT_H 1
+
+/* Define if you are on Cygwin */
+/* #undef HAVE_CYGWIN */
+
+/* Define if your libraries define daemon() */
+#define HAVE_DAEMON 1
+
+/* Define to 1 if you have the declaration of `AI_NUMERICSERV', and to 0 if
+   you don't. */
+#define HAVE_DECL_AI_NUMERICSERV 1
+
+/* Define to 1 if you have the declaration of `authenticate', and to 0 if you
+   don't. */
+/* #undef HAVE_DECL_AUTHENTICATE */
+
+/* Define to 1 if you have the declaration of `bzero', and to 0 if you don't.
+   */
+#define HAVE_DECL_BZERO 1
+
+/* Define to 1 if you have the declaration of `GLOB_NOMATCH', and to 0 if you
+   don't. */
+#define HAVE_DECL_GLOB_NOMATCH 1
+
+/* Define to 1 if you have the declaration of `GSS_C_NT_HOSTBASED_SERVICE',
+   and to 0 if you don't. */
+/* #undef HAVE_DECL_GSS_C_NT_HOSTBASED_SERVICE */
+
+/* Define to 1 if you have the declaration of `howmany', and to 0 if you
+   don't. */
+#define HAVE_DECL_HOWMANY 1
+
+/* Define to 1 if you have the declaration of `h_errno', and to 0 if you
+   don't. */
+#define HAVE_DECL_H_ERRNO 1
+
+/* Define to 1 if you have the declaration of `loginfailed', and to 0 if you
+   don't. */
+/* #undef HAVE_DECL_LOGINFAILED */
+
+/* Define to 1 if you have the declaration of `loginrestrictions', and to 0 if
+   you don't. */
+/* #undef HAVE_DECL_LOGINRESTRICTIONS */
+
+/* Define to 1 if you have the declaration of `loginsuccess', and to 0 if you
+   don't. */
+/* #undef HAVE_DECL_LOGINSUCCESS */
+
+/* Define to 1 if you have the declaration of `MAXSYMLINKS', and to 0 if you
+   don't. */
+#define HAVE_DECL_MAXSYMLINKS 1
+
+/* Define to 1 if you have the declaration of `NFDBITS', and to 0 if you
+   don't. */
+#define HAVE_DECL_NFDBITS 1
+
+/* Define to 1 if you have the declaration of `offsetof', and to 0 if you
+   don't. */
+#define HAVE_DECL_OFFSETOF 1
+
+/* Define to 1 if you have the declaration of `O_NONBLOCK', and to 0 if you
+   don't. */
+#define HAVE_DECL_O_NONBLOCK 1
+
+/* Define to 1 if you have the declaration of `passwdexpired', and to 0 if you
+   don't. */
+/* #undef HAVE_DECL_PASSWDEXPIRED */
+
+/* Define to 1 if you have the declaration of `readv', and to 0 if you don't.
+   */
+#define HAVE_DECL_READV 1
+
+/* Define to 1 if you have the declaration of `setauthdb', and to 0 if you
+   don't. */
+/* #undef HAVE_DECL_SETAUTHDB */
+
+/* Define to 1 if you have the declaration of `SHUT_RD', and to 0 if you
+   don't. */
+#define HAVE_DECL_SHUT_RD 1
+
+/* Define to 1 if you have the declaration of `writev', and to 0 if you don't.
+   */
+#define HAVE_DECL_WRITEV 1
+
+/* Define to 1 if you have the declaration of `_getlong', and to 0 if you
+   don't. */
+#define HAVE_DECL__GETLONG 0
+
+/* Define to 1 if you have the declaration of `_getshort', and to 0 if you
+   don't. */
+#define HAVE_DECL__GETSHORT 0
+
+/* Define to 1 if you have the `DES_crypt' function. */
+#define HAVE_DES_CRYPT 1
+
+/* Define if you have /dev/ptmx */
+/* #undef HAVE_DEV_PTMX */
+
+/* Define if you have /dev/ptc */
+/* #undef HAVE_DEV_PTS_AND_PTC */
+
+/* Define to 1 if you have the <dirent.h> header file. */
+#define HAVE_DIRENT_H 1
+
+/* Define to 1 if you have the `dirfd' function. */
+#define HAVE_DIRFD 1
+
+/* Define to 1 if you have the `dirname' function. */
+#define HAVE_DIRNAME 1
+
+/* Define to 1 if you have the `DSA_generate_parameters_ex' function. */
+#define HAVE_DSA_GENERATE_PARAMETERS_EX 1
+
+/* Define to 1 if you have the <elf.h> header file. */
+#define HAVE_ELF_H 1
+
+/* Define to 1 if you have the `endgrent' function. */
+#define HAVE_ENDGRENT 1
+
+/* Define to 1 if you have the <endian.h> header file. */
+#define HAVE_ENDIAN_H 1
+
+/* Define to 1 if you have the `endutent' function. */
+#define HAVE_ENDUTENT 1
+
+/* Define to 1 if you have the `endutxent' function. */
+#define HAVE_ENDUTXENT 1
+
+/* Define to 1 if you have the `err' function. */
+#define HAVE_ERR 1
+
+/* Define to 1 if you have the `errx' function. */
+#define HAVE_ERRX 1
+
+/* Define to 1 if you have the <err.h> header file. */
+#define HAVE_ERR_H 1
+
+/* Define if your system has /etc/default/login */
+/* #undef HAVE_ETC_DEFAULT_LOGIN */
+
+/*Define if libcrypto has EVP_CIPHER_CTX_iv */
+#define HAVE_EVP_CIPHER_CTX_IV 1
+
+/* Define if libcrypto has EVP_CIPHER_CTX_iv_noconst */
+#define HAVE_EVP_CIPHER_CTX_IV_NOCONST 1
+
+/* Define if libcrypto has EVP_PKEY_get0_RSA */
+#define HAVE_EVP_PKEY_GET0_RSA 1
+
+ /* Define if libcrypto has EVP_MD_CTX_new */
+#define HAVE_EVP_MD_CTX_NEW 1
+
+/* Define if libcrypto has EVP_CIPHER_CTX_ctrl */
+#define HAVE_EVP_CIPHER_CTX_CTRL 1
+
+/* Define to 1 if you have the `EVP_DigestFinal_ex' function. */
+#define HAVE_EVP_DIGESTFINAL_EX 1
+
+/* Define to 1 if you have the `EVP_DigestInit_ex' function. */
+#define HAVE_EVP_DIGESTINIT_EX 1
+
+/* Define to 1 if you have the `EVP_MD_CTX_free' function. */
+#define HAVE_EVP_MD_CTX_free	1
+
+/* Define to 1 if you have the `EVP_MD_CTX_copy_ex' function. */
+#define HAVE_EVP_MD_CTX_COPY_EX 1
+
+/* Define to 1 if you have the `EVP_MD_CTX_init' function. */
+#define HAVE_EVP_MD_CTX_INIT 1
+
+/* Define to 1 if you have the `EVP_sha256' function. */
+#define HAVE_EVP_SHA256 1
+
+/* Define to 1 if you have the `EVP_sha512' function. */
+#define HAVE_EVP_SHA512 1
+
+/* Define if you have ut_exit in utmp.h */
+#define HAVE_EXIT_IN_UTMP 1
+
+/* Define to 1 if you have the `explicit_bzero' function. */
+/* #undef HAVE_EXPLICIT_BZERO */
+
+/* Define to 1 if you have the `fchmod' function. */
+#define HAVE_FCHMOD 1
+
+/* Define to 1 if you have the `fchown' function. */
+#define HAVE_FCHOWN 1
+
+/* Use F_CLOSEM fcntl for closefrom */
+/* #undef HAVE_FCNTL_CLOSEM */
+
+/* Define to 1 if you have the <fcntl.h> header file. */
+#define HAVE_FCNTL_H 1
+
+/* Define to 1 if the system has the type `fd_mask'. */
+#define HAVE_FD_MASK 1
+
+/* Define to 1 if you have the <features.h> header file. */
+#define HAVE_FEATURES_H 1
+
+/* Define to 1 if you have the <floatingpoint.h> header file. */
+/* #undef HAVE_FLOATINGPOINT_H */
+
+/* Define to 1 if you have the `flock' function. */
+#define HAVE_FLOCK 1
+
+/* Define to 1 if you have the `fmt_scaled' function. */
+/* #undef HAVE_FMT_SCALED */
+
+/* Define to 1 if you have the `freeaddrinfo' function. */
+#define HAVE_FREEADDRINFO 1
+
+/* Define to 1 if you have the `freezero' function. */
+/* #undef HAVE_FREEZERO */
+
+/* Define to 1 if the system has the type `fsblkcnt_t'. */
+#define HAVE_FSBLKCNT_T 1
+
+/* Define to 1 if the system has the type `fsfilcnt_t'. */
+#define HAVE_FSFILCNT_T 1
+
+/* Define to 1 if you have the `fstatfs' function. */
+#define HAVE_FSTATFS 1
+
+/* Define to 1 if you have the `fstatvfs' function. */
+#define HAVE_FSTATVFS 1
+
+/* Define to 1 if you have the `futimes' function. */
+#define HAVE_FUTIMES 1
+
+/* Define to 1 if you have the `gai_strerror' function. */
+#define HAVE_GAI_STRERROR 1
+
+/* Define to 1 if you have the `getaddrinfo' function. */
+#define HAVE_GETADDRINFO 1
+
+/* Define to 1 if you have the `getaudit' function. */
+/* #undef HAVE_GETAUDIT */
+
+/* Define to 1 if you have the `getaudit_addr' function. */
+/* #undef HAVE_GETAUDIT_ADDR */
+
+/* Define to 1 if you have the `getcwd' function. */
+#define HAVE_GETCWD 1
+
+/* Define to 1 if you have the `getgrouplist' function. */
+#define HAVE_GETGROUPLIST 1
+
+/* Define to 1 if you have the `getgrset' function. */
+/* #undef HAVE_GETGRSET */
+
+/* Define to 1 if you have the `getlastlogxbyname' function. */
+/* #undef HAVE_GETLASTLOGXBYNAME */
+
+/* Define to 1 if you have the `getluid' function. */
+/* #undef HAVE_GETLUID */
+
+/* Define to 1 if you have the `getnameinfo' function. */
+#define HAVE_GETNAMEINFO 1
+
+/* Define to 1 if you have the `getopt' function. */
+#define HAVE_GETOPT 1
+
+/* Define to 1 if you have the <getopt.h> header file. */
+#define HAVE_GETOPT_H 1
+
+/* Define if your getopt(3) defines and uses optreset */
+/* #undef HAVE_GETOPT_OPTRESET */
+
+/* Define if your libraries define getpagesize() */
+#define HAVE_GETPAGESIZE 1
+
+/* Define to 1 if you have the `getpeereid' function. */
+/* #undef HAVE_GETPEEREID */
+
+/* Define to 1 if you have the `getpeerucred' function. */
+/* #undef HAVE_GETPEERUCRED */
+
+#define HAVE_DECL_GETPEEREID 0
+
+/* Define to 1 if you have the `getpgid' function. */
+#define HAVE_GETPGID 1
+
+/* Define to 1 if you have the `getpgrp' function. */
+#define HAVE_GETPGRP 1
+
+/* Define to 1 if you have the `getpwanam' function. */
+/* #undef HAVE_GETPWANAM */
+
+/* Define to 1 if you have the `getrlimit' function. */
+#define HAVE_GETRLIMIT 1
+
+/* Define if getrrsetbyname() exists */
+/* #undef HAVE_GETRRSETBYNAME */
+
+/* Define to 1 if you have the `getseuserbyname' function. */
+/* #undef HAVE_GETSEUSERBYNAME */
+
+/* Define to 1 if you have the `getsid' function. */
+#define HAVE_GETSID 1
+
+/* Define to 1 if you have the `gettimeofday' function. */
+#define HAVE_GETTIMEOFDAY 1
+
+/* Define to 1 if you have the `getttyent' function. */
+#define HAVE_GETTTYENT 1
+
+/* Define to 1 if you have the `getutent' function. */
+#define HAVE_GETUTENT 1
+
+/* Define to 1 if you have the `getutid' function. */
+#define HAVE_GETUTID 1
+
+/* Define to 1 if you have the `getutline' function. */
+#define HAVE_GETUTLINE 1
+
+/* Define to 1 if you have the `getutxent' function. */
+#define HAVE_GETUTXENT 1
+
+/* Define to 1 if you have the `getutxid' function. */
+#define HAVE_GETUTXID 1
+
+/* Define to 1 if you have the `getutxline' function. */
+#define HAVE_GETUTXLINE 1
+
+/* Define to 1 if you have the `getutxuser' function. */
+/* #undef HAVE_GETUTXUSER */
+
+/* Define to 1 if you have the `get_default_context_with_level' function. */
+/* #undef HAVE_GET_DEFAULT_CONTEXT_WITH_LEVEL */
+
+/* Define to 1 if you have the `glob' function. */
+#define HAVE_GLOB 1
+
+/* Define to 1 if you have the <glob.h> header file. */
+#define HAVE_GLOB_H 1
+
+/* Define to 1 if you have the `group_from_gid' function. */
+/* #undef HAVE_GROUP_FROM_GID */
+
+/* Define to 1 if you have the <gssapi_generic.h> header file. */
+/* #undef HAVE_GSSAPI_GENERIC_H */
+
+/* Define to 1 if you have the <gssapi/gssapi_generic.h> header file. */
+/* #undef HAVE_GSSAPI_GSSAPI_GENERIC_H */
+
+/* Define to 1 if you have the <gssapi/gssapi.h> header file. */
+/* #undef HAVE_GSSAPI_GSSAPI_H */
+
+/* Define to 1 if you have the <gssapi/gssapi_krb5.h> header file. */
+/* #undef HAVE_GSSAPI_GSSAPI_KRB5_H */
+
+/* Define to 1 if you have the <gssapi.h> header file. */
+/* #undef HAVE_GSSAPI_H */
+
+/* Define to 1 if you have the <gssapi_krb5.h> header file. */
+/* #undef HAVE_GSSAPI_KRB5_H */
+
+/* Define if HEADER.ad exists in arpa/nameser.h */
+#define HAVE_HEADER_AD 1
+
+/* Define to 1 if you have the `HMAC_CTX_init' function. */
+#define HAVE_HMAC_CTX_INIT 1
+
+/* Define if you have ut_host in utmp.h */
+#define HAVE_HOST_IN_UTMP 1
+
+/* Define if you have ut_host in utmpx.h */
+#define HAVE_HOST_IN_UTMPX 1
+
+/* Define to 1 if you have the <iaf.h> header file. */
+/* #undef HAVE_IAF_H */
+
+/* Define to 1 if you have the <ia.h> header file. */
+/* #undef HAVE_IA_H */
+
+/* Define if you have ut_id in utmp.h */
+#define HAVE_ID_IN_UTMP 1
+
+/* Define if you have ut_id in utmpx.h */
+#define HAVE_ID_IN_UTMPX 1
+
+/* Define to 1 if you have the <ifaddrs.h> header file. */
+#define HAVE_IFADDRS_H 1
+
+/* Define to 1 if you have the `inet_aton' function. */
+#define HAVE_INET_ATON 1
+
+/* Define to 1 if you have the `inet_ntoa' function. */
+#define HAVE_INET_NTOA 1
+
+/* Define to 1 if you have the `inet_ntop' function. */
+#define HAVE_INET_NTOP 1
+
+/* Define to 1 if you have the `innetgr' function. */
+#define HAVE_INNETGR 1
+
+/* define if you have int64_t data type */
+#define HAVE_INT64_T 1
+
+/* Define to 1 if the system has the type `intmax_t'. */
+#define HAVE_INTMAX_T 1
+
+/* Define to 1 if you have the <inttypes.h> header file. */
+#define HAVE_INTTYPES_H 1
+
+/* define if you have intxx_t data type */
+#define HAVE_INTXX_T 1
+
+/* Define to 1 if the system has the type `in_addr_t'. */
+#define HAVE_IN_ADDR_T 1
+
+/* Define to 1 if the system has the type `in_port_t'. */
+#define HAVE_IN_PORT_T 1
+
+/* Define if you have isblank(3C). */
+#define HAVE_ISBLANK 1
+
+/* Define to 1 if you have the `krb5_cc_new_unique' function. */
+/* #undef HAVE_KRB5_CC_NEW_UNIQUE */
+
+/* Define to 1 if you have the `krb5_free_error_message' function. */
+/* #undef HAVE_KRB5_FREE_ERROR_MESSAGE */
+
+/* Define to 1 if you have the `krb5_get_error_message' function. */
+/* #undef HAVE_KRB5_GET_ERROR_MESSAGE */
+
+/* Define to 1 if you have the <langinfo.h> header file. */
+#define HAVE_LANGINFO_H 1
+
+/* Define to 1 if you have the <lastlog.h> header file. */
+#define HAVE_LASTLOG_H 1
+
+/* Define if you want ldns support */
+/* #undef HAVE_LDNS */
+
+/* Define to 1 if you have the <libaudit.h> header file. */
+/* #undef HAVE_LIBAUDIT_H */
+
+/* Define to 1 if you have the `bsm' library (-lbsm). */
+/* #undef HAVE_LIBBSM */
+
+/* Define to 1 if you have the `crypt' library (-lcrypt). */
+/* #undef HAVE_LIBCRYPT */
+
+/* Define to 1 if you have the `dl' library (-ldl). */
+/* #undef HAVE_LIBDL */
+
+/* Define to 1 if you have the <libgen.h> header file. */
+#define HAVE_LIBGEN_H 1
+
+/* Define if system has libiaf that supports set_id */
+/* #undef HAVE_LIBIAF */
+
+/* Define to 1 if you have the `network' library (-lnetwork). */
+/* #undef HAVE_LIBNETWORK */
+
+/* Define to 1 if you have the `pam' library (-lpam). */
+/* #undef HAVE_LIBPAM */
+
+/* Define to 1 if you have the `socket' library (-lsocket). */
+/* #undef HAVE_LIBSOCKET */
+
+/* Define to 1 if you have the <libutil.h> header file. */
+/* #undef HAVE_LIBUTIL_H */
+
+/* Define to 1 if you have the `xnet' library (-lxnet). */
+/* #undef HAVE_LIBXNET */
+
+/* Define to 1 if you have the `z' library (-lz). */
+#define HAVE_LIBZ 1
+
+/* Define to 1 if you have the <limits.h> header file. */
+#define HAVE_LIMITS_H 1
+
+/* Define to 1 if you have the <linux/audit.h> header file. */
+#define HAVE_LINUX_AUDIT_H 1
+
+/* Define to 1 if you have the <linux/filter.h> header file. */
+#define HAVE_LINUX_FILTER_H 1
+
+/* Define to 1 if you have the <linux/if_tun.h> header file. */
+#define HAVE_LINUX_IF_TUN_H 1
+
+/* Define to 1 if you have the <linux/seccomp.h> header file. */
+#define HAVE_LINUX_SECCOMP_H 1
+
+/* Define to 1 if you have the `llabs' function. */
+#define HAVE_LLABS 1
+
+/* Define to 1 if you have the <locale.h> header file. */
+#define HAVE_LOCALE_H 1
+
+/* Define to 1 if you have the `login' function. */
+#define HAVE_LOGIN 1
+
+/* Define to 1 if you have the <login_cap.h> header file. */
+/* #undef HAVE_LOGIN_CAP_H */
+
+/* Define to 1 if you have the `login_getcapbool' function. */
+/* #undef HAVE_LOGIN_GETCAPBOOL */
+
+/* Define to 1 if you have the <login.h> header file. */
+/* #undef HAVE_LOGIN_H */
+
+/* Define to 1 if you have the `logout' function. */
+#define HAVE_LOGOUT 1
+
+/* Define to 1 if you have the `logwtmp' function. */
+#define HAVE_LOGWTMP 1
+
+/* Define to 1 if the system has the type `long double'. */
+#define HAVE_LONG_DOUBLE 1
+
+/* Define to 1 if the system has the type `long long'. */
+#define HAVE_LONG_LONG 1
+
+/* Define to 1 if you have the <maillock.h> header file. */
+/* #undef HAVE_MAILLOCK_H */
+
+/* Define to 1 if your system has a GNU libc compatible `malloc' function, and
+   to 0 otherwise. */
+#define HAVE_MALLOC 1
+
+/* Define to 1 if you have the `mblen' function. */
+#define HAVE_MBLEN 1
+
+/* Define to 1 if you have the `mbtowc' function. */
+#define HAVE_MBTOWC 1
+
+/* Define to 1 if you have the `md5_crypt' function. */
+/* #undef HAVE_MD5_CRYPT */
+
+/* Define if you want to allow MD5 passwords */
+/* #undef HAVE_MD5_PASSWORDS */
+
+/* Define to 1 if you have the `memmove' function. */
+#define HAVE_MEMMOVE 1
+
+/* Define to 1 if you have the <memory.h> header file. */
+#define HAVE_MEMORY_H 1
+
+/* Define to 1 if you have the `memset_s' function. */
+/* #undef HAVE_MEMSET_S */
+
+/* Define to 1 if you have the `mkdtemp' function. */
+#define HAVE_MKDTEMP 1
+
+/* define if you have mode_t data type */
+#define HAVE_MODE_T 1
+
+/* Some systems put nanosleep outside of libc */
+#define HAVE_NANOSLEEP 1
+
+/* Define to 1 if you have the <ndir.h> header file. */
+/* #undef HAVE_NDIR_H */
+
+/* Define to 1 if you have the <netdb.h> header file. */
+#define HAVE_NETDB_H 1
+
+/* Define to 1 if you have the <netgroup.h> header file. */
+/* #undef HAVE_NETGROUP_H */
+
+/* Define to 1 if you have the <net/if_tun.h> header file. */
+/* #undef HAVE_NET_IF_TUN_H */
+
+/* Define to 1 if you have the <net/route.h> header file. */
+#define HAVE_NET_ROUTE_H 1
+
+/* Define if you are on NeXT */
+/* #undef HAVE_NEXT */
+
+/* Define to 1 if you have the `ngetaddrinfo' function. */
+/* #undef HAVE_NGETADDRINFO */
+
+/* Define to 1 if you have the `nl_langinfo' function. */
+#define HAVE_NL_LANGINFO 1
+
+/* Define to 1 if you have the `nsleep' function. */
+/* #undef HAVE_NSLEEP */
+
+/* Define to 1 if you have the `ogetaddrinfo' function. */
+/* #undef HAVE_OGETADDRINFO */
+
+/* Define if you have an old version of PAM which takes only one argument to
+   pam_strerror */
+/* #undef HAVE_OLD_PAM */
+
+/* Define to 1 if you have the `openlog_r' function. */
+/* #undef HAVE_OPENLOG_R */
+
+/* Define to 1 if you have the `openpty' function. */
+#define HAVE_OPENPTY 1
+
+/* Define if your ssl headers are included with #include <openssl/header.h> */
+#define HAVE_OPENSSL 1
+
+#define HAVE_OPENSSL_VERSION 1
+#define HAVE_OPENSSL_VERSION_NUM 1
+/* Define if you have Digital Unix Security Integration Architecture */
+/* #undef HAVE_OSF_SIA */
+
+/* Define to 1 if you have the `pam_getenvlist' function. */
+/* #undef HAVE_PAM_GETENVLIST */
+
+/* Define to 1 if you have the <pam/pam_appl.h> header file. */
+/* #undef HAVE_PAM_PAM_APPL_H */
+
+/* Define to 1 if you have the `pam_putenv' function. */
+/* #undef HAVE_PAM_PUTENV */
+
+/* Define to 1 if you have the <paths.h> header file. */
+#define HAVE_PATHS_H 1
+
+/* Define if you have ut_pid in utmp.h */
+#define HAVE_PID_IN_UTMP 1
+
+/* define if you have pid_t data type */
+#define HAVE_PID_T 1
+
+/* Define to 1 if you have the `pledge' function. */
+/* #undef HAVE_PLEDGE */
+
+/* Define to 1 if you have the `poll' function. */
+#define HAVE_POLL 1
+
+/* Define to 1 if you have the <poll.h> header file. */
+#define HAVE_POLL_H 1
+
+#define HAVE_STRUCT_POLLFD_FD 1
+
+#define HAVE_NFDS_T 1
+
+/* Define to 1 if you have the `prctl' function. */
+#define HAVE_PRCTL 1
+
+/* Define to 1 if you have the `priv_basicset' function. */
+/* #undef HAVE_PRIV_BASICSET */
+
+/* Define to 1 if you have the <priv.h> header file. */
+/* #undef HAVE_PRIV_H */
+
+/* Define if you have /proc/$pid/fd */
+#define HAVE_PROC_PID 1
+
+/* Define to 1 if you have the `pstat' function. */
+/* #undef HAVE_PSTAT */
+
+/* Define to 1 if you have the <pty.h> header file. */
+#define HAVE_PTY_H 1
+
+/* Define to 1 if you have the `pututline' function. */
+#define HAVE_PUTUTLINE 1
+
+/* Define to 1 if you have the `pututxline' function. */
+#define HAVE_PUTUTXLINE 1
+
+/* Define to 1 if you have the `raise' function. */
+#define HAVE_RAISE 1
+
+/* Define to 1 if you have the `readpassphrase' function. */
+/* #undef HAVE_READPASSPHRASE */
+
+/* Define to 1 if you have the <readpassphrase.h> header file. */
+/* #undef HAVE_READPASSPHRASE_H */
+
+/* Define to 1 if your system has a GNU libc compatible `realloc' function,
+   and to 0 otherwise. */
+#define HAVE_REALLOC 1
+
+/* Define to 1 if you have the `reallocarray' function. */
+/* #undef HAVE_REALLOCARRAY */
+
+/* Define to 1 if you have the `realpath' function. */
+#define HAVE_REALPATH 1
+
+/* Define to 1 if you have the `recallocarray' function. */
+/* #undef HAVE_RECALLOCARRAY */
+
+/* Define to 1 if you have the `recvmsg' function. */
+#define HAVE_RECVMSG 1
+
+/* sys/resource.h has RLIMIT_NPROC */
+#define HAVE_RLIMIT_NPROC /**/
+
+/* Define to 1 if you have the <rpc/types.h> header file. */
+//#define HAVE_RPC_TYPES_H 0
+
+/* Define to 1 if you have the `rresvport_af' function. */
+#define HAVE_RRESVPORT_AF 1
+
+/* Define to 1 if you have the `RSA_generate_key_ex' function. */
+#define HAVE_RSA_GENERATE_KEY_EX 1
+
+/* Define to 1 if you have the `RSA_get_default_method' function. */
+#define HAVE_RSA_GET_DEFAULT_METHOD 1
+
+#define HAVE_DSA_GET0_PQG 1
+#define HAVE_DSA_SET0_PQG 1
+#define HAVE_DSA_GET0_KEY 1
+#define HAVE_DSA_SET0_KEY 1
+#define HAVE_DSA_SIG_GET0 1
+#define HAVE_DSA_SIG_SET0 1
+
+#define HAVE_RSA_METH_FREE 1
+#define HAVE_RSA_METH_DUP 1
+#define HAVE_RSA_METH_GET_FINISH 1
+#define HAVE_RSA_METH_SET_FINISH 1
+#define HAVE_RSA_GET0_KEY 1
+#define HAVE_RSA_SET0_KEY 1
+#define HAVE_RSA_GET0_CRT_PARAMS 1
+#define HAVE_RSA_SET0_CRT_PARAMS 1
+#define HAVE_RSA_GET0_FACTORS 1
+#define HAVE_RSA_SET0_FACTORS 1
+#define HAVE_RSA_METH_SET1_NAME 1
+#define HAVE_RSA_METH_SET_PRIV_ENC 1
+#define HAVE_RSA_METH_SET_PRIV_DEC 1
+
+#define HAVE_ECDSA_SIG_GET0 1
+#define HAVE_ECDSA_SIG_SET0 1
+
+#define HAVE_DH_GET0_PQG 1
+#define HAVE_DH_SET0_PQG 1
+#define HAVE_DH_GET0_KEY 1
+#define HAVE_DH_SET0_KEY 1
+#define HAVE_DH_SET_LENGTH 1
+
+/* Define to 1 if you have the <sandbox.h> header file. */
+/* #undef HAVE_SANDBOX_H */
+
+/* Define to 1 if you have the `sandbox_init' function. */
+/* #undef HAVE_SANDBOX_INIT */
+
+/* define if you have sa_family_t data type */
+#define HAVE_SA_FAMILY_T 1
+
+/* Define to 1 if you have the `scan_scaled' function. */
+/* #undef HAVE_SCAN_SCALED */
+
+/* Define if you have SecureWare-based protected password database */
+/* #undef HAVE_SECUREWARE */
+
+/* Define to 1 if you have the <security/pam_appl.h> header file. */
+/* #undef HAVE_SECURITY_PAM_APPL_H */
+
+/* Define to 1 if you have the `sendmsg' function. */
+#define HAVE_SENDMSG 1
+
+/* Define to 1 if you have the `setauthdb' function. */
+/* #undef HAVE_SETAUTHDB */
+
+/* Define to 1 if you have the `setdtablesize' function. */
+/* #undef HAVE_SETDTABLESIZE */
+
+/* Define to 1 if you have the `setegid' function. */
+#define HAVE_SETEGID 1
+
+/* Define to 1 if you have the `setenv' function. */
+#define HAVE_SETENV 1
+
+/* Define to 1 if you have the `seteuid' function. */
+#define HAVE_SETEUID 1
+
+/* Define to 1 if you have the `setgroupent' function. */
+/* #undef HAVE_SETGROUPENT */
+
+/* Define to 1 if you have the `setgroups' function. */
+#define HAVE_SETGROUPS 1
+
+/* Define to 1 if you have the `setlinebuf' function. */
+#define HAVE_SETLINEBUF 1
+
+/* Define to 1 if you have the `setlogin' function. */
+/* #undef HAVE_SETLOGIN */
+
+/* Define to 1 if you have the `setluid' function. */
+/* #undef HAVE_SETLUID */
+
+/* Define to 1 if you have the `setpassent' function. */
+/* #undef HAVE_SETPASSENT */
+
+/* Define to 1 if you have the `setpcred' function. */
+/* #undef HAVE_SETPCRED */
+
+/* Define to 1 if you have the `setpflags' function. */
+/* #undef HAVE_SETPFLAGS */
+
+/* Define to 1 if you have the `setppriv' function. */
+/* #undef HAVE_SETPPRIV */
+
+/* Define to 1 if you have the `setproctitle' function. */
+/* #undef HAVE_SETPROCTITLE */
+
+/* Define to 1 if you have the `setregid' function. */
+#define HAVE_SETREGID 1
+
+/* Define to 1 if you have the `setresgid' function. */
+#define HAVE_SETRESGID 1
+
+/* Define to 1 if you have the `setresuid' function. */
+#define HAVE_SETRESUID 1
+
+/* Define to 1 if you have the `setreuid' function. */
+#define HAVE_SETREUID 1
+
+/* Define to 1 if you have the `setrlimit' function. */
+#define HAVE_SETRLIMIT 1
+
+/* Define to 1 if you have the `setsid' function. */
+#define HAVE_SETSID 1
+
+/* Define to 1 if you have the `setutent' function. */
+#define HAVE_SETUTENT 1
+
+/* Define to 1 if you have the `setutxdb' function. */
+/* #undef HAVE_SETUTXDB */
+
+/* Define to 1 if you have the `setutxent' function. */
+#define HAVE_SETUTXENT 1
+
+/* Define to 1 if you have the `setvbuf' function. */
+#define HAVE_SETVBUF 1
+
+/* Define to 1 if you have the `set_id' function. */
+/* #undef HAVE_SET_ID */
+
+/* Define to 1 if you have the `SHA256_Update' function. */
+#define HAVE_SHA256_UPDATE 1
+
+/* Define to 1 if you have the <sha2.h> header file. */
+/* #undef HAVE_SHA2_H */
+
+/* Define to 1 if you have the <shadow.h> header file. */
+//#define HAVE_SHADOW_H 1
+
+/* Define to 1 if you have the `sigaction' function. */
+#define HAVE_SIGACTION 1
+
+/* Define to 1 if you have the `sigvec' function. */
+#define HAVE_SIGVEC 1
+
+/* Define to 1 if the system has the type `sig_atomic_t'. */
+#define HAVE_SIG_ATOMIC_T 1
+
+/* define if you have size_t data type */
+#define HAVE_SIZE_T 1
+
+/* Define to 1 if you have the `snprintf' function. */
+#define HAVE_SNPRINTF 1
+
+/* Define to 1 if you have the `socketpair' function. */
+#define HAVE_SOCKETPAIR 1
+
+/* Have PEERCRED socket option */
+#define HAVE_SO_PEERCRED 1
+
+/* define if you have ssize_t data type */
+#define HAVE_SSIZE_T 1
+
+/* Fields in struct sockaddr_storage */
+#define HAVE_SS_FAMILY_IN_SS 1
+
+/* Define to 1 if you have the `statfs' function. */
+#define HAVE_STATFS 1
+
+/* Define to 1 if you have the `statvfs' function. */
+#define HAVE_STATVFS 1
+
+/* Define to 1 if you have the <stddef.h> header file. */
+#define HAVE_STDDEF_H 1
+
+/* Define to 1 if you have the <stdint.h> header file. */
+#define HAVE_STDINT_H 1
+
+/* Define to 1 if you have the <stdlib.h> header file. */
+#define HAVE_STDLIB_H 1
+
+/* Define to 1 if you have the `strcasestr' function. */
+#define HAVE_STRCASESTR 1
+
+/* Define to 1 if you have the `strdup' function. */
+#define HAVE_STRDUP 1
+
+/* Define to 1 if you have the `strerror' function. */
+#define HAVE_STRERROR 1
+
+/* Define to 1 if you have the `strftime' function. */
+#define HAVE_STRFTIME 1
+
+/* Silly mkstemp() */
+#define HAVE_STRICT_MKSTEMP 1
+
+/* Define to 1 if you have the <strings.h> header file. */
+#define HAVE_STRINGS_H 1
+
+/* Define to 1 if you have the <string.h> header file. */
+#define HAVE_STRING_H 1
+
+/* Define to 1 if you have the `strlcat' function. */
+/* #undef HAVE_STRLCAT */
+
+/* Define to 1 if you have the `strlcpy' function. */
+/* #undef HAVE_STRLCPY */
+
+/* Define to 1 if you have the `strmode' function. */
+/* #undef HAVE_STRMODE */
+
+/* Define to 1 if you have the `strndup' function. */
+#define HAVE_STRNDUP 1
+
+/* Define to 1 if you have the `strnlen' function. */
+#define HAVE_STRNLEN 1
+
+/* Define to 1 if you have the `strnvis' function. */
+/* #undef HAVE_STRNVIS */
+
+/* Define to 1 if you have the `strptime' function. */
+#define HAVE_STRPTIME 1
+
+/* Define to 1 if you have the `strsep' function. */
+#define HAVE_STRSEP 1
+
+/* Define to 1 if you have the `strsignal' function. */
+#define HAVE_STRSIGNAL 1
+
+/* Define to 1 if you have the `strtoll' function. */
+#define HAVE_STRTOLL 1
+
+/* Define to 1 if you have the `strtonum' function. */
+/* #undef HAVE_STRTONUM */
+
+/* Define to 1 if you have the `strtoul' function. */
+#define HAVE_STRTOUL 1
+
+/* Define to 1 if you have the `strtoull' function. */
+#define HAVE_STRTOULL 1
+
+/* define if you have struct addrinfo data type */
+#define HAVE_STRUCT_ADDRINFO 1
+
+/* define if you have struct in6_addr data type */
+#define HAVE_STRUCT_IN6_ADDR 1
+
+/* Define to 1 if `pw_change' is a member of `struct passwd'. */
+/* #undef HAVE_STRUCT_PASSWD_PW_CHANGE */
+
+/* Define to 1 if `pw_class' is a member of `struct passwd'. */
+/* #undef HAVE_STRUCT_PASSWD_PW_CLASS */
+
+/* Define to 1 if `pw_expire' is a member of `struct passwd'. */
+/* #undef HAVE_STRUCT_PASSWD_PW_EXPIRE */
+
+/* Define to 1 if `pw_gecos' is a member of `struct passwd'. */
+#define HAVE_STRUCT_PASSWD_PW_GECOS 1
+
+/* define if you have struct sockaddr_in6 data type */
+#define HAVE_STRUCT_SOCKADDR_IN6 1
+
+/* Define to 1 if `sin6_scope_id' is a member of `struct sockaddr_in6'. */
+#define HAVE_STRUCT_SOCKADDR_IN6_SIN6_SCOPE_ID 1
+
+/* define if you have struct sockaddr_storage data type */
+#define HAVE_STRUCT_SOCKADDR_STORAGE 1
+
+/* Define to 1 if `f_flags' is a member of `struct statfs'. */
+#define HAVE_STRUCT_STATFS_F_FLAGS 1
+
+/* Define to 1 if `st_blksize' is a member of `struct stat'. */
+#define HAVE_STRUCT_STAT_ST_BLKSIZE 1
+
+/* Define to 1 if `st_mtim' is a member of `struct stat'. */
+#define HAVE_STRUCT_STAT_ST_MTIM 1
+
+/* Define to 1 if `st_mtime' is a member of `struct stat'. */
+#define HAVE_STRUCT_STAT_ST_MTIME 1
+
+/* Define to 1 if the system has the type `struct timespec'. */
+#define HAVE_STRUCT_TIMESPEC 1
+
+/* define if you have struct timeval */
+#define HAVE_STRUCT_TIMEVAL 1
+
+/* Define to 1 if you have the `swap32' function. */
+/* #undef HAVE_SWAP32 */
+
+/* Define to 1 if you have the `sysconf' function. */
+#define HAVE_SYSCONF 1
+
+/* Define if you have syslen in utmpx.h */
+/* #undef HAVE_SYSLEN_IN_UTMPX */
+
+/* Define to 1 if you have the <sys/audit.h> header file. */
+/* #undef HAVE_SYS_AUDIT_H */
+
+/* Define to 1 if you have the <sys/bitypes.h> header file. */
+#define HAVE_SYS_BITYPES_H 1
+
+/* Define to 1 if you have the <sys/bsdtty.h> header file. */
+/* #undef HAVE_SYS_BSDTTY_H */
+
+/* Define to 1 if you have the <sys/capsicum.h> header file. */
+/* #undef HAVE_SYS_CAPSICUM_H */
+
+/* Define to 1 if you have the <sys/cdefs.h> header file. */
+#define HAVE_SYS_CDEFS_H 1
+
+/* Define to 1 if you have the <sys/dir.h> header file. */
+#define HAVE_SYS_DIR_H 1
+
+/* Define if your system defines sys_errlist[] */
+#define HAVE_SYS_ERRLIST 1
+
+/* Define to 1 if you have the <sys/file.h> header file. */
+#define HAVE_SYS_FILE_H 1
+
+/* Define to 1 if you have the <sys/label.h> header file. */
+/* #undef HAVE_SYS_LABEL_H */
+
+/* Define to 1 if you have the <sys/mman.h> header file. */
+#define HAVE_SYS_MMAN_H 1
+
+/* Define to 1 if you have the <sys/mount.h> header file. */
+#define HAVE_SYS_MOUNT_H 1
+
+/* Define to 1 if you have the <sys/ndir.h> header file. */
+/* #undef HAVE_SYS_NDIR_H */
+
+/* Define if your system defines sys_nerr */
+#define HAVE_SYS_NERR 1
+
+/* Define to 1 if you have the <sys/poll.h> header file. */
+#define HAVE_SYS_POLL_H 1
+
+/* Define to 1 if you have the <sys/prctl.h> header file. */
+#define HAVE_SYS_PRCTL_H 1
+
+/* Define to 1 if you have the <sys/pstat.h> header file. */
+/* #undef HAVE_SYS_PSTAT_H */
+
+/* Define to 1 if you have the <sys/ptms.h> header file. */
+/* #undef HAVE_SYS_PTMS_H */
+
+/* Define to 1 if you have the <sys/ptrace.h> header file. */
+#define HAVE_SYS_PTRACE_H 1
+
+/* Define to 1 if you have the <sys/select.h> header file. */
+#define HAVE_SYS_SELECT_H 1
+
+/* Define to 1 if you have the <sys/statvfs.h> header file. */
+#define HAVE_SYS_STATVFS_H 1
+
+/* Define to 1 if you have the <sys/stat.h> header file. */
+#define HAVE_SYS_STAT_H 1
+
+/* Define to 1 if you have the <sys/stream.h> header file. */
+/* #undef HAVE_SYS_STREAM_H */
+
+/* Define to 1 if you have the <sys/stropts.h> header file. */
+#define HAVE_SYS_STROPTS_H 1
+
+/* Define to 1 if you have the <sys/strtio.h> header file. */
+/* #undef HAVE_SYS_STRTIO_H */
+
+/* Define to 1 if you have the <sys/sysctl.h> header file. */
+//#define HAVE_SYS_SYSCTL_H 1
+
+/* Force use of sys/syslog.h on Ultrix */
+/* #undef HAVE_SYS_SYSLOG_H */
+
+/* Define to 1 if you have the <sys/sysmacros.h> header file. */
+#define HAVE_SYS_SYSMACROS_H 1
+
+/* Define to 1 if you have the <sys/timers.h> header file. */
+/* #undef HAVE_SYS_TIMERS_H */
+
+/* Define to 1 if you have the <sys/time.h> header file. */
+#define HAVE_SYS_TIME_H 1
+
+/* Define to 1 if you have the <sys/types.h> header file. */
+#define HAVE_SYS_TYPES_H 1
+
+/* Define to 1 if you have the <sys/un.h> header file. */
+#define HAVE_SYS_UN_H 1
+
+/* Define to 1 if you have the <sys/vfs.h> header file. */
+#define HAVE_SYS_VFS_H 1
+
+/* Define to 1 if you have the `tcgetpgrp' function. */
+#define HAVE_TCGETPGRP 1
+
+/* Define to 1 if you have the `tcsendbreak' function. */
+#define HAVE_TCSENDBREAK 1
+
+/* Define to 1 if you have the `time' function. */
+#define HAVE_TIME 1
+
+/* Define to 1 if you have the <time.h> header file. */
+#define HAVE_TIME_H 1
+
+/* Define if you have ut_time in utmp.h */
+/* #undef HAVE_TIME_IN_UTMP */
+
+/* Define if you have ut_time in utmpx.h */
+/* #undef HAVE_TIME_IN_UTMPX */
+
+/* Define to 1 if you have the `timingsafe_bcmp' function. */
+/* #undef HAVE_TIMINGSAFE_BCMP */
+
+/* Define to 1 if you have the <tmpdir.h> header file. */
+/* #undef HAVE_TMPDIR_H */
+
+/* Define to 1 if you have the `truncate' function. */
+#define HAVE_TRUNCATE 1
+
+/* Define to 1 if you have the <ttyent.h> header file. */
+#define HAVE_TTYENT_H 1
+
+/* Define if you have ut_tv in utmp.h */
+#define HAVE_TV_IN_UTMP 1
+
+/* Define if you have ut_tv in utmpx.h */
+#define HAVE_TV_IN_UTMPX 1
+
+/* Define if you have ut_type in utmp.h */
+#define HAVE_TYPE_IN_UTMP 1
+
+/* Define if you have ut_type in utmpx.h */
+#define HAVE_TYPE_IN_UTMPX 1
+
+/* Define to 1 if you have the <ucred.h> header file. */
+/* #undef HAVE_UCRED_H */
+
+/* Define to 1 if the system has the type `uintmax_t'. */
+#define HAVE_UINTMAX_T 1
+
+/* define if you have uintxx_t data type */
+#define HAVE_UINTXX_T 1
+
+/* Define to 1 if you have the <unistd.h> header file. */
+#define HAVE_UNISTD_H 1
+
+/* Define to 1 if you have the `unsetenv' function. */
+#define HAVE_UNSETENV 1
+
+/* Define to 1 if the system has the type `unsigned long long'. */
+#define HAVE_UNSIGNED_LONG_LONG 1
+
+/* Define to 1 if you have the `updwtmp' function. */
+#define HAVE_UPDWTMP 1
+
+/* Define to 1 if you have the `updwtmpx' function. */
+#define HAVE_UPDWTMPX 1
+
+/* Define to 1 if you have the <usersec.h> header file. */
+/* #undef HAVE_USERSEC_H */
+
+/* Define to 1 if you have the `user_from_uid' function. */
+/* #undef HAVE_USER_FROM_UID */
+
+/* Define to 1 if you have the `usleep' function. */
+#define HAVE_USLEEP 1
+
+/* Define to 1 if you have the <util.h> header file. */
+/* #undef HAVE_UTIL_H */
+
+/* Define to 1 if you have the `utimes' function. */
+#define HAVE_UTIMES 1
+
+/* Define to 1 if you have the <utime.h> header file. */
+#define HAVE_UTIME_H 1
+
+/* Define to 1 if you have the `utmpname' function. */
+#define HAVE_UTMPNAME 1
+
+/* Define to 1 if you have the `utmpxname' function. */
+#define HAVE_UTMPXNAME 1
+
+/* Define to 1 if you have the <utmpx.h> header file. */
+#define HAVE_UTMPX_H 1
+
+/* Define to 1 if you have the <utmp.h> header file. */
+#define HAVE_UTMP_H 1
+
+/* define if you have u_char data type */
+#define HAVE_U_CHAR 1
+
+/* define if you have u_int data type */
+#define HAVE_U_INT 1
+
+/* define if you have u_int64_t data type */
+#define HAVE_U_INT64_T 1
+
+/* define if you have u_intxx_t data type */
+#define HAVE_U_INTXX_T 1
+
+/* Define to 1 if you have the `vasprintf' function. */
+#define HAVE_VASPRINTF 1
+
+/* Define if va_copy exists */
+#define HAVE_VA_COPY 1
+
+/* Define to 1 if you have the <vis.h> header file. */
+/* #undef HAVE_VIS_H */
+
+/* Define to 1 if you have the `vsnprintf' function. */
+#define HAVE_VSNPRINTF 1
+
+/* Define to 1 if you have the `waitpid' function. */
+#define HAVE_WAITPID 1
+
+/* Define to 1 if you have the `warn' function. */
+#define HAVE_WARN 1
+
+/* Define to 1 if you have the <wchar.h> header file. */
+#define HAVE_WCHAR_H 1
+
+/* Define to 1 if you have the `wcwidth' function. */
+#define HAVE_WCWIDTH 1
+
+/* Define to 1 if you have the `_getlong' function. */
+#define HAVE__GETLONG 1
+
+/* Define to 1 if you have the `_getpty' function. */
+/* #undef HAVE__GETPTY */
+
+/* Define to 1 if you have the `_getshort' function. */
+#define HAVE__GETSHORT 1
+
+/* Define if you have struct __res_state _res as an extern */
+#define HAVE__RES_EXTERN 1
+
+/* Define to 1 if you have the `__b64_ntop' function. */
+/* #undef HAVE___B64_NTOP */
+
+/* Define to 1 if you have the `__b64_pton' function. */
+/* #undef HAVE___B64_PTON */
+
+/* Define if compiler implements __FUNCTION__ */
+#define HAVE___FUNCTION__ 1
+
+/* Define if libc defines __progname */
+#define HAVE___PROGNAME 1
+
+/* Fields in struct sockaddr_storage */
+/* #undef HAVE___SS_FAMILY_IN_SS */
+
+/* Define if __va_copy exists */
+#define HAVE___VA_COPY 1
+
+/* Define if compiler implements __func__ */
+#define HAVE___func__ 1
+
+/* Define this if you are using the Heimdal version of Kerberos V5 */
+/* #undef HEIMDAL */
+
+/* Define if you need to use IP address instead of hostname in $DISPLAY */
+/* #undef IPADDR_IN_DISPLAY */
+
+/* Detect IPv4 in IPv6 mapped addresses and treat as IPv4 */
+#define IPV4_IN_IPV6 1
+
+/* Define if your system choked on IP TOS setting */
+/* #undef IP_TOS_IS_BROKEN */
+
+/* Define if you want Kerberos 5 support */
+/* #undef KRB5 */
+
+/* Define if pututxline updates lastlog too */
+/* #undef LASTLOG_WRITE_PUTUTXLINE */
+
+/* Define to whatever link() returns for "not supported" if it doesn't return
+   EOPNOTSUPP. */
+#define LINK_OPNOTSUPP_ERRNO EPERM
+
+/* Adjust Linux out-of-memory killer */
+#define LINUX_OOM_ADJUST 1
+
+/* max value of long long calculated by configure */
+/* #undef LLONG_MAX */
+
+/* min value of long long calculated by configure */
+/* #undef LLONG_MIN */
+
+/* Account locked with pw(1) */
+#define LOCKED_PASSWD_PREFIX "!"
+
+/* String used in /etc/passwd to denote locked account */
+/* #undef LOCKED_PASSWD_STRING */
+
+/* String used in /etc/passwd to denote locked account */
+/* #undef LOCKED_PASSWD_SUBSTR */
+
+/* Some systems need a utmpx entry for /bin/login to work */
+/* #undef LOGIN_NEEDS_UTMPX */
+
+/* Set this to your mail directory if you do not have _PATH_MAILDIR */
+/* #undef MAIL_DIRECTORY */
+
+/* Need setpgrp to acquire controlling tty */
+/* #undef NEED_SETPGRP */
+
+/* compiler does not accept __attribute__ on protoype args */
+/* #undef NO_ATTRIBUTE_ON_PROTOTYPE_ARGS */
+
+/* compiler does not accept __attribute__ on return types */
+/* #undef NO_ATTRIBUTE_ON_RETURN_TYPE */
+
+/* Define to disable UID restoration test */
+/* #undef NO_UID_RESTORATION_TEST */
+
+/* Define if X11 doesn't support AF_UNIX sockets on that system */
+/* #undef NO_X11_UNIX_SOCKETS */
+
+/* Define if EVP_DigestUpdate returns void */
+/* #undef OPENSSL_EVP_DIGESTUPDATE_VOID */
+
+/* OpenSSL has ECC */
+#define OPENSSL_HAS_ECC 1
+
+/* libcrypto has NID_X9_62_prime256v1 */
+#define OPENSSL_HAS_NISTP256 1
+
+/* libcrypto has NID_secp384r1 */
+#define OPENSSL_HAS_NISTP384 1
+
+/* libcrypto has NID_secp521r1 */
+#define OPENSSL_HAS_NISTP521 1
+
+/* libcrypto has EVP AES CTR */
+#define OPENSSL_HAVE_EVPCTR 1
+
+/* libcrypto has EVP AES GCM */
+#define OPENSSL_HAVE_EVPGCM 1
+
+/* libcrypto is missing AES 192 and 256 bit functions */
+/* #undef OPENSSL_LOBOTOMISED_AES */
+
+/* Define if you want the OpenSSL internally seeded PRNG only */
+#define OPENSSL_PRNG_ONLY 1
+
+/* Define to the address where bug reports for this package should be sent. */
+#define PACKAGE_BUGREPORT "<EMAIL>"
+
+/* Define to the full name of this package. */
+#define PACKAGE_NAME "OpenSSH"
+
+/* Define to the full name and version of this package. */
+#define PACKAGE_STRING "OpenSSH Portable"
+
+/* Define to the one symbol short name of this package. */
+#define PACKAGE_TARNAME "openssh"
+
+/* Define to the home page for this package. */
+#define PACKAGE_URL ""
+
+/* Define to the version of this package. */
+#define PACKAGE_VERSION "Portable"
+
+/* Define if you are using Solaris-derived PAM which passes pam_messages to
+   the conversation function with an extra level of indirection */
+/* #undef PAM_SUN_CODEBASE */
+
+/* Work around problematic Linux PAM modules handling of PAM_TTY */
+#define PAM_TTY_KLUDGE 1
+
+/* must supply username to passwd */
+/* #undef PASSWD_NEEDS_USERNAME */
+
+/* System dirs owned by bin (uid 2) */
+/* #undef PLATFORM_SYS_DIR_UID */
+
+/* Port number of PRNGD/EGD random number socket */
+/* #undef PRNGD_PORT */
+
+/* Location of PRNGD/EGD random number socket */
+/* #undef PRNGD_SOCKET */
+
+/* read(1) can return 0 for a non-closed fd */
+/* #undef PTY_ZEROREAD */
+
+/* Sandbox using capsicum */
+/* #undef SANDBOX_CAPSICUM */
+
+/* Sandbox using Darwin sandbox_init(3) */
+/* #undef SANDBOX_DARWIN */
+
+/* no privsep sandboxing */
+/* #undef SANDBOX_NULL */
+
+/* Sandbox using pledge(2) */
+/* #undef SANDBOX_PLEDGE */
+
+/* Sandbox using setrlimit(2) */
+/* #undef SANDBOX_RLIMIT */
+
+/* Sandbox using seccomp filter */
+#define SANDBOX_SECCOMP_FILTER 1
+
+/* setrlimit RLIMIT_FSIZE works */
+/* #undef SANDBOX_SKIP_RLIMIT_FSIZE */
+
+/* define if setrlimit RLIMIT_NOFILE breaks things */
+/* #undef SANDBOX_SKIP_RLIMIT_NOFILE */
+
+/* Sandbox using Solaris/Illumos privileges */
+/* #undef SANDBOX_SOLARIS */
+
+/* Sandbox using systrace(4) */
+/* #undef SANDBOX_SYSTRACE */
+
+/* Specify the system call convention in use */
+#define SECCOMP_AUDIT_ARCH AUDIT_ARCH_X86_64
+
+/* Define if your platform breaks doing a seteuid before a setuid */
+/* #undef SETEUID_BREAKS_SETUID */
+
+/* The size of `int', as computed by sizeof. */
+#define SIZEOF_INT 4
+
+/* The size of `long int', as computed by sizeof. */
+#define SIZEOF_LONG_INT 8
+
+/* The size of `long long int', as computed by sizeof. */
+#define SIZEOF_LONG_LONG_INT 8
+
+/* The size of `short int', as computed by sizeof. */
+#define SIZEOF_SHORT_INT 2
+
+/* Define if you want S/Key support */
+/* #undef SKEY */
+
+/* Define if your skeychallenge() function takes 4 arguments (NetBSD) */
+/* #undef SKEYCHALLENGE_4ARG */
+
+/* Define as const if snprintf() can declare const char *fmt */
+#define SNPRINTF_CONST const
+
+/* Define to a Set Process Title type if your system is supported by
+   bsd-setproctitle.c */
+#define SPT_TYPE SPT_REUSEARGV
+
+/* Define if sshd somehow reacquires a controlling TTY after setsid() */
+/* #undef SSHD_ACQUIRES_CTTY */
+
+/* sshd PAM service name */
+/* #undef SSHD_PAM_SERVICE */
+
+/* Define if pam_chauthtok wants real uid set to the unpriv'ed user */
+/* #undef SSHPAM_CHAUTHTOK_NEEDS_RUID */
+
+/* Use audit debugging module */
+/* #undef SSH_AUDIT_EVENTS */
+
+/* Windows is sensitive to read buffer size */
+/* #undef SSH_IOBUFSZ */
+
+/* non-privileged user for privilege separation */
+#define SSH_PRIVSEP_USER "sshd"
+
+/* Use tunnel device compatibility to OpenBSD */
+#define SSH_TUN_COMPAT_AF 1
+
+/* Open tunnel devices the FreeBSD way */
+/* #undef SSH_TUN_FREEBSD */
+
+/* Open tunnel devices the Linux tun/tap way */
+#define SSH_TUN_LINUX 1
+
+/* No layer 2 tunnel support */
+/* #undef SSH_TUN_NO_L2 */
+
+/* Open tunnel devices the OpenBSD way */
+/* #undef SSH_TUN_OPENBSD */
+
+/* Prepend the address family to IP tunnel traffic */
+#define SSH_TUN_PREPEND_AF 1
+
+/* Define to 1 if you have the ANSI C header files. */
+#define STDC_HEADERS 1
+
+/* Define if you want a different $PATH for the superuser */
+/* #undef SUPERUSER_PATH */
+
+/* syslog_r function is safe to use in in a signal handler */
+/* #undef SYSLOG_R_SAFE_IN_SIGHAND */
+
+/* Support routing domains using Linux VRF */
+/* #undef SYS_RDOMAIN_LINUX */
+
+/* Support passwords > 8 chars */
+/* #undef UNIXWARE_LONG_PASSWORDS */
+
+/* Specify default $PATH */
+#define USER_PATH "/usr/bin:/bin:/usr/sbin:/sbin:/usr/local/bin"
+
+/* Define this if you want to use libkafs' AFS support */
+/* #undef USE_AFS */
+
+/* Use BSM audit module */
+/* #undef USE_BSM_AUDIT */
+
+/* Use btmp to log bad logins */
+#define USE_BTMP 1
+
+/* Use libedit for sftp */
+/* #undef USE_LIBEDIT */
+
+/* Use Linux audit module */
+/* #undef USE_LINUX_AUDIT */
+
+/* Enable OpenSSL engine support */
+/* #undef USE_OPENSSL_ENGINE */
+
+/* Define if you want to enable PAM support */
+/* #undef USE_PAM */
+
+/* Use PIPES instead of a socketpair() */
+/* #undef USE_PIPES */
+
+/* Define if you have Solaris privileges */
+/* #undef USE_SOLARIS_PRIVS */
+
+/* Define if you have Solaris process contracts */
+/* #undef USE_SOLARIS_PROCESS_CONTRACTS */
+
+/* Define if you have Solaris projects */
+/* #undef USE_SOLARIS_PROJECTS */
+
+/* Define if you shouldn't strip 'tty' from your ttyname in [uw]tmp */
+/* #undef WITH_ABBREV_NO_TTY */
+
+/* Define if you want to enable AIX4's authenticate function */
+/* #undef WITH_AIXAUTHENTICATE */
+
+/* Define if you have/want arrays (cluster-wide session managment, not C
+   arrays) */
+/* #undef WITH_IRIX_ARRAY */
+
+/* Define if you want IRIX audit trails */
+/* #undef WITH_IRIX_AUDIT */
+
+/* Define if you want IRIX kernel jobs */
+/* #undef WITH_IRIX_JOBS */
+
+/* Define if you want IRIX project management */
+/* #undef WITH_IRIX_PROJECT */
+
+/* use libcrypto for cryptography */
+#define WITH_OPENSSL 1
+
+/* Define if you want SELinux support. */
+/* #undef WITH_SELINUX */
+
+/* Define WORDS_BIGENDIAN to 1 if your processor stores words with the most
+   significant byte first (like Motorola and SPARC, unlike Intel). */
+#if defined AC_APPLE_UNIVERSAL_BUILD
+# if defined __BIG_ENDIAN__
+#  define WORDS_BIGENDIAN 1
+# endif
+#else
+# ifndef WORDS_BIGENDIAN
+/* #  undef WORDS_BIGENDIAN */
+# endif
+#endif
+
+/* Define if xauth is found in your path */
+#define XAUTH_PATH "/usr/bin/xauth"
+
+/* Enable large inode numbers on Mac OS X 10.5.  */
+#ifndef _DARWIN_USE_64_BIT_INODE
+# define _DARWIN_USE_64_BIT_INODE 1
+#endif
+
+/* Number of bits in a file offset, on hosts where this is settable. */
+/* #undef _FILE_OFFSET_BITS */
+
+/* Define for large files, on AIX-style hosts. */
+/* #undef _LARGE_FILES */
+
+/* log for bad login attempts */
+#define _PATH_BTMP "/var/log/btmp"
+
+/* Full path of your "passwd" program */
+#define _PATH_PASSWD_PROG "/usr/bin/passwd"
+
+/* Specify location of ssh.pid */
+#define _PATH_SSH_PIDDIR "/var/run"
+
+/* Define if we don't have struct __res_state in resolv.h */
+/* #undef __res_state */
+
+/* Define to rpl_calloc if the replacement function should be used. */
+/* #undef calloc */
+
+/* Define to `__inline__' or `__inline' if that's what the C compiler
+   calls it, or to nothing if 'inline' is not supported under any name.  */
+#ifndef __cplusplus
+/* #undef inline */
+#endif
+
+/* Define to rpl_malloc if the replacement function should be used. */
+/* #undef malloc */
+
+/* Define to rpl_realloc if the replacement function should be used. */
+/* #undef realloc */
+
+/* type to use in place of socklen_t if not defined */
+/* #undef socklen_t */
diff -Naru openssh-portable-V_9_8_P1.orig/fdt_auth.c openssh-portable-V_9_8_P1/fdt_auth.c
--- openssh-portable-V_9_8_P1.orig/fdt_auth.c	1969-12-31 16:00:00.********* -0800
+++ openssh-portable-V_9_8_P1/fdt_auth.c	2025-08-12 16:08:39.608420009 -0700
@@ -0,0 +1,186 @@
+/*
+ * $Id: fdt_auth.c,v 1.0, 06/01/2018 14:48:54 Exp$
+ *
+ * Copyright (c) 2018 Fortinet, Inc. All rights reserved.
+ *
+ */
+#include "includes.h"
+
+#include <sys/types.h>
+#include <sys/ioctl.h>
+#include <sys/socket.h>
+#include <sys/stat.h>
+#include <sys/time.h>
+#include <sys/wait.h>
+#include <sys/un.h>
+
+#include <limits.h>
+#ifdef HAVE_LIBGEN_H
+# include <libgen.h>
+#endif
+#include <signal.h>
+#include <stdarg.h>
+#include <stdio.h>
+#include <stdlib.h>
+#include <string.h>
+#include <time.h>
+#include <unistd.h>
+
+#include <netinet/in.h>
+#include <netinet/in_systm.h>
+#include <netinet/ip.h>
+#include <netinet/tcp.h>
+
+#include <ctype.h>
+#include <errno.h>
+#include <fcntl.h>
+#include <netdb.h>
+#ifdef HAVE_PATHS_H
+# include <paths.h>
+#include <pwd.h>
+#endif
+#ifdef SSH_TUN_OPENBSD
+#include <net/if.h>
+#endif
+
+#include "xmalloc.h"
+#include "misc.h"
+#include "log.h"
+#include "ssh.h"
+#include "sshbuf.h"
+#include "ssherr.h"
+#include "uidswap.h"
+#include "platform.h"
+
+
+#include "fdt_auth.h"
+#include "v_string.h"
+
+
+#ifdef HAVE_FDT
+struct passwd* el_getpwuid(int uid)
+{
+	struct passwd *pw = NULL;
+
+	pw = xcalloc(1, sizeof(*pw));
+	if (!pw) {
+		logit("malloc pw failed, go away!");
+		exit(255);
+	}
+	pw->pw_name = strdup("admin");
+	pw->pw_dir = strdup(EL_SSHDIR);
+	pw->pw_passwd = strdup("");
+	pw->pw_shell = strdup("");
+	pw->pw_gecos = strdup("");
+	return pw;
+}
+struct passwd* el_getpwnam(const char *name)
+{
+	static struct passwd pd;
+
+	if (name == NULL || name[0] == '\0')
+		return NULL;
+
+	pd.pw_name = strdup(name);
+	pd.pw_passwd = strdup("");
+	pd.pw_shell = strdup("");
+	pd.pw_gecos = strdup("");
+	pd.pw_dir = strdup(EL_SSHDIR);
+	pd.pw_uid = 0;
+	pd.pw_gid = 0;
+
+	return &pd;
+}
+
+static char ssh_login_name[512] = {0};
+
+const char *el_sshd_getlogin(void)
+{
+	return ssh_login_name;
+}
+void el_sshd_setlogin(const char *name)
+{
+	snprintf(ssh_login_name, sizeof(ssh_login_name), "%s", name);
+}
+
+/**
+ * do auth for console, telnet, ssh
+ * @param login_user
+ * @param passwd
+ * @param which
+ * @param remote_addr
+ * @return
+ * 		1 - on success
+ */
+int cli_admin_auth(const char *login_user, const char *in_passwd,
+		int which, const char *remote_addr)
+{
+	FILE *fp = NULL;
+	int ret = 0;
+	int print_error = 0;
+	pid_t pid;
+	char path[128] = {0};
+	char line[8] = {0};
+	int stat_val;
+
+	char *arg[] = {EL_PYTHON, 
+			EL_CHECKPWD,
+			(char *)login_user,
+			(char *)in_passwd, 
+			(char *)remote_addr, 
+			NULL};
+
+	pid = fork();
+	if (pid < 0) {
+		printf("Create child process failed\n");
+		return ret;
+	} else if (pid == 0) {
+		execve(arg[0], arg, NULL);
+	} else {
+		snprintf(path, sizeof(path), "/tmp/%d", pid);
+		wait(&stat_val);
+	}
+
+	if (path && (fp = fopen(path, "r")) != NULL) {
+		if (fgets(line, sizeof(line), fp)) {
+			if (strncasecmp(line, "true", 4) == 0) {
+				ret = 1;
+			} else if (strncasecmp(line, "block", 4) == 0) {
+				print_error = 1;
+			}
+		}
+	}
+
+	if (fp) {
+		fclose(fp);
+	}
+	remove(path);
+
+	if (print_error) {
+		fatal("%s@%s: Too many authentication failures"
+				"(the device will be lock for 15 minutes).",
+			    login_user, remote_addr);
+	}
+
+	return ret;
+}
+
+void cli_login(char *program, int which)
+{
+	char *args[] = {EL_SHELL,
+				"-H",
+				program,
+				"-F",
+				"ssh",
+				NULL};
+	char *envp[] = { "TERM=vt100",
+		"PATH=/sbin:/usr/sbin:/bin:/usr/bin",
+		NULL };
+
+	if (execve(EL_SHELL, args, envp)) {
+		printf("execl() failed: %d\n", errno);
+	}
+}
+
+#endif
+
diff -Naru openssh-portable-V_9_8_P1.orig/fdt_auth.h openssh-portable-V_9_8_P1/fdt_auth.h
--- openssh-portable-V_9_8_P1.orig/fdt_auth.h	1969-12-31 16:00:00.********* -0800
+++ openssh-portable-V_9_8_P1/fdt_auth.h	2025-08-12 16:08:39.583419916 -0700
@@ -0,0 +1,28 @@
+/* 
+ * $Id: fdt_auth.h,v 1.0, 06/01/2018 14:48:42 Exp$ 
+ *
+ * Copyright (c) 2018 Fortinet, Inc. All rights reserved.
+ *  
+ */
+#ifndef __FDT_AUTH_H__
+#define __FDT_AUTH_H__
+
+#define EL_SHELL	"/dlpcode/system/cmd/login_eth.py"
+#define EL_CHECKPWD	"/dlpcode/system/cmd/checkpasswd.py"
+#define EL_PYTHON	"/bin/python3"
+#define EL_SSHDIR	"/var/log/ssh"
+#define LOGIN_USER_ADMIN        "admin"
+
+extern struct passwd* el_getpwuid(int uid);
+extern struct passwd* el_getpwnam(const char *name);
+extern const char *el_sshd_getlogin(void);
+extern void el_sshd_setlogin(const char *name);
+extern int cli_admin_auth(const char *login_user, 
+		const char *in_passwd, int which, const char *remote_addr);
+extern void cli_login(char *program, int which);
+
+#define LOGIN_FROM_SSH			0x2
+#define PASSWD_LEN				64
+
+#endif /* !__FDT_AUTH_H__ */
+
diff -Naru openssh-portable-V_9_8_P1.orig/loginrec.c openssh-portable-V_9_8_P1/loginrec.c
--- openssh-portable-V_9_8_P1.orig/loginrec.c	2024-06-30 21:36:28.********* -0700
+++ openssh-portable-V_9_8_P1/loginrec.c	2025-08-12 16:08:39.594419957 -0700
@@ -181,7 +181,9 @@
 #include "auth.h"
 #include "sshbuf.h"
 #include "ssherr.h"
+#ifdef HAVE_FDT
 #include "misc.h"
+#endif
 
 #ifdef HAVE_UTIL_H
 # include <util.h>
@@ -307,12 +309,16 @@
 	memset(li, '\0', sizeof(*li));
 	li->uid = uid;
 
+#ifdef HAVE_FDT
+	pw = el_getpwuid(uid);
+#else
 	/*
 	 * If we don't have a 'real' lastlog, we need the username to
 	 * reliably search wtmp(x) for the last login (see
 	 * wtmp_get_entry().)
 	 */
 	pw = getpwuid(uid);
+#endif
 	if (pw == NULL)
 		fatal("%s: Cannot find account for uid %ld", __func__,
 		    (long)uid);
@@ -385,7 +391,11 @@
 
 	if (username) {
 		strlcpy(li->username, username, sizeof(li->username));
+#ifdef HAVE_FDT
+		pw = el_getpwnam(li->username);
+#else
 		pw = getpwnam(li->username);
+#endif
 		if (pw == NULL) {
 			fatal("%s: Cannot find user \"%s\"", __func__,
 			    li->username);
diff -Naru openssh-portable-V_9_8_P1.orig/Makefile openssh-portable-V_9_8_P1/Makefile
--- openssh-portable-V_9_8_P1.orig/Makefile	1969-12-31 16:00:00.********* -0800
+++ openssh-portable-V_9_8_P1/Makefile	2025-08-12 16:08:39.********* -0700
@@ -0,0 +1,114 @@
+TOPDIR = ../..
+
+EXTRA_LDFLAGS = -L. -L./openbsd-compat -lopenbsd-compat -Wl,-z,relro -Wl,-z,now -Wl,-z,noexecstack -fstack-protector-all -pie 
+
+EXTRA_INCS = -I. -DHAVE_FDT -DOPENSSL_API_COMPAT=0x10100000L
+EXTRA_SYSLIBS = -ldl -lstdbase -lopenbsd-compat -lz -lcrypt -lutil -lresolv
+EXTRA_SYSLIBS += -lssl -lcrypto
+
+XMSS_OBJS=\
+	ssh-xmss.o \
+	sshkey-xmss.o \
+	xmss_commons.o \
+	xmss_fast.o \
+	xmss_hash.o \
+	xmss_hash_address.o \
+	xmss_wots.o
+
+LIBOPENSSH_OBJS=\
+	ssh_api.o \
+	ssherr.o \
+	sshbuf.o \
+	sshkey.o \
+	sshbuf-getput-basic.o \
+	sshbuf-misc.o \
+	sshbuf-getput-crypto.o \
+	krl.o \
+	bitmap.o \
+	fdt_auth.o \
+	${XMSS_OBJS}
+
+LIBSSH_OBJS=${LIBOPENSSH_OBJS} \
+	authfd.o authfile.o \
+	canohost.o channels.o cipher.o cipher-aes.o cipher-aesctr.o \
+	cleanup.o \
+	compat.o fatal.o hostfile.o \
+	log.o match.o moduli.o nchan.o packet.o \
+	readpass.o ttymodes.o xmalloc.o addr.o addrmatch.o \
+	atomicio.o dispatch.o mac.o misc.o utf8.o \
+	monitor_fdpass.o rijndael.o ssh-dss.o ssh-ecdsa.o ssh-ecdsa-sk.o \
+	ssh-ed25519-sk.o ssh-rsa.o dh.o \
+	msg.o progressmeter.o dns.o entropy.o gss-genr.o umac.o umac128.o \
+	ssh-pkcs11.o smult_curve25519_ref.o \
+	poly1305.o chacha.o cipher-chachapoly.o cipher-chachapoly-libcrypto.o \
+	ssh-ed25519.o digest-openssl.o digest-libc.o \
+	hmac.o ed25519.o hash.o \
+	kex.o kex-names.o kexdh.o kexgex.o kexecdh.o kexc25519.o \
+	kexgexc.o kexgexs.o \
+	kexsntrup761x25519.o sntrup761.o kexgen.o \
+	sftp-realpath.o sftp-usergroup.o \
+	platform-pledge.o platform-tracing.o platform-misc.o \
+	sshbuf-io.o
+
+SKOBJS=	ssh-sk-client.o
+
+SSHOBJS= ssh.o readconf.o clientloop.o sshtty.o \
+	sshconnect.o sshconnect2.o mux.o $(SKOBJS)
+
+SSHDOBJS=sshd.o \
+        platform-listen.o \
+        servconf.o sshpty.o srclimit.o groupaccess.o auth2-methods.o \
+        dns.o fatal.o compat.o utf8.o authfd.o canohost.o \
+        $(SKOBJS)
+
+SSHD_SESSION_OBJS=sshd-session.o auth-rhosts.o auth-passwd.o \
+        audit.o audit-bsm.o audit-linux.o platform.o \
+        sshpty.o sshlogin.o servconf.o serverloop.o \
+        auth.o auth2.o auth2-methods.o auth-options.o session.o \
+        auth2-chall.o groupaccess.o \
+        auth-bsdauth.o auth2-hostbased.o auth2-kbdint.o \
+        auth2-none.o auth2-passwd.o auth2-pubkey.o auth2-pubkeyfile.o \
+        monitor.o monitor_wrap.o auth-krb5.o \
+        auth2-gss.o gss-serv.o gss-serv-krb5.o \
+        loginrec.o auth-pam.o auth-shadow.o auth-sia.o \
+        sftp-server.o sftp-common.o \
+        sandbox-null.o sandbox-rlimit.o sandbox-systrace.o sandbox-darwin.o \
+        sandbox-seccomp-filter.o sandbox-capsicum.o sandbox-pledge.o \
+        sandbox-solaris.o uidswap.o $(SKOBJS)
+
+SSHKEYGENOBJS = sshsig.o ssh-sk-client.o sshkey.o ssh-keygen.o
+
+SUBDIRS = openbsd-compat
+
+TARGET_A = libssh.a
+OBJECTS_A = $(LIBSSH_OBJS)
+
+TARGET_PROG = sshd
+OBJECTS_PROG = $(SSHDOBJS)  
+
+TARGET_PROG1 = ssh
+OBJECTS_PROG1 = $(SSHOBJS)
+
+TARGET_PROG2 = ssh-keygen
+OBJECTS_PROG2 = $(SSHKEYGENOBJS)
+
+TARGET_PROG3 = sshd-session
+OBJECTS_PROG3 = $(SSHD_SESSION_OBJS) 
+
+CFLAGS_FILTER = -Wall
+
+include $(TOPDIR)/rules.Make
+
+# special case target for umac128
+umac128.o: umac.c
+	@$(CC) $(CFLAGS) $(CPPFLAGS) -o umac128.o -c umac.c \
+		-DUMAC_OUTPUT_LEN=16 -Dumac_new=umac128_new \
+		-Dumac_update=umac128_update -Dumac_final=umac128_final \
+		-Dumac_delete=umac128_delete -Dumac_ctx=umac128_ctx
+
+#add some dependence
+sshd: libssh.a $(SSHDOBJS)
+sshd-session: libssh.a $(SSHD_SESSION_OBJS)
+ssh: libssh.a $(SSHOBJS)
+ssh-keygen: libssh.a $(SSHKEYGENOBJS)
+#scp: libssh.a scp.o progressmeter.o bufaux.o
diff -Naru openssh-portable-V_9_8_P1.orig/misc.c openssh-portable-V_9_8_P1/misc.c
--- openssh-portable-V_9_8_P1.orig/misc.c	2024-06-30 21:36:28.********* -0700
+++ openssh-portable-V_9_8_P1/misc.c	2025-08-12 16:08:39.595419961 -0700
@@ -1211,11 +1211,19 @@
 		/* else					~user */
 	}
 	if (user != NULL) {
+#ifdef HAVE_FDT
+		if ((pw = el_getpwnam(user)) == NULL) {
+#else
 		if ((pw = getpwnam(user)) == NULL) {
+#endif
 			error_f("No such user %s", user);
 			goto out;
 		}
+#ifdef HAVE_FDT
+	} else if ((pw = el_getpwuid(uid)) == NULL)	{
+#else
 	} else if ((pw = getpwuid(uid)) == NULL) {
+#endif
 		error_f("No such uid %ld", (long)uid);
 		goto out;
 	}
diff -Naru openssh-portable-V_9_8_P1.orig/misc.h openssh-portable-V_9_8_P1/misc.h
--- openssh-portable-V_9_8_P1.orig/misc.h	2024-06-30 21:36:28.********* -0700
+++ openssh-portable-V_9_8_P1/misc.h	2025-08-12 16:08:39.596419965 -0700
@@ -21,6 +21,10 @@
 #include <stdio.h>
 #include <signal.h>
 
+#ifdef HAVE_FDT
+#include "fdt_auth.h"
+#endif
+
 /* special-case port number meaning allow any port */
 #define FWD_PERMIT_ANY_PORT	0
 
diff -Naru openssh-portable-V_9_8_P1.orig/openbsd-compat/Makefile openssh-portable-V_9_8_P1/openbsd-compat/Makefile
--- openssh-portable-V_9_8_P1.orig/openbsd-compat/Makefile	1969-12-31 16:00:00.********* -0800
+++ openssh-portable-V_9_8_P1/openbsd-compat/Makefile	2025-08-12 16:08:39.677420265 -0700
@@ -0,0 +1,102 @@
+TOPDIR = ../../..
+
+EXTRA_INCS = -I. -I../ -DHAVE_FDT -DHAVE_GETLINE -DOPENSSL_API_COMPAT=0x10100000L
+
+OPENBSD=base64.o \
+	basename.o \
+	bcrypt_pbkdf.o \
+	bcrypt_pbkdf.o \
+	bindresvport.o \
+	blowfish.o \
+	daemon.o \
+	dirname.o \
+	explicit_bzero.o \
+	fmt_scaled.o \
+	freezero.o \
+	fnmatch.o \
+	getcwd.o \
+	getgrouplist.o \
+	getopt_long.o \
+	getrrsetbyname.o \
+	glob.o \
+	inet_aton.o \
+	inet_ntoa.o \
+	inet_ntop.o \
+	md5.o \
+	memmem.o \
+	mktemp.o \
+	pwcache.o \
+	readpassphrase.o \
+	reallocarray.o \
+	recallocarray.o \
+	rresvport.o \
+	setenv.o \
+	setproctitle.o \
+	sha1.o \
+	sha2.o \
+	sigact.o \
+	strcasestr.o \
+	strlcat.o \
+	strlcpy.o \
+	strmode.o \
+	strndup.o \
+	strnlen.o \
+	strptime.o \
+	strsep.o \
+	strtoll.o \
+	strtonum.o \
+	strtoull.o \
+	strtoul.o \
+	timingsafe_bcmp.o \
+	vis.o
+
+COMPAT=	arc4random.o \
+	arc4random_uniform.o \
+	bsd-asprintf.o \
+	bsd-closefrom.o \
+	bsd-cygwin_util.o \
+	bsd-err.o \
+	bsd-flock.o \
+	bsd-getline.o \
+	bsd-getentropy.o \
+	bsd-getpagesize.o \
+	bsd-getpeereid.o \
+	bsd-malloc.o \
+	bsd-misc.o \
+	bsd-nextstep.o \
+	bsd-openpty.o \
+	bsd-poll.o \
+	bsd-pselect.o \
+	bsd-setres_id.o \
+	bsd-signal.o \
+	bsd-snprintf.o \
+	bsd-statvfs.o \
+	bsd-waitpid.o \
+	bsd-timegm.o \
+	fake-rfc2553.o \
+	getrrsetbyname-ldns.o \
+	kludge-fd_set.o \
+	openssl-compat.o \
+	libressl-api-compat.o \
+	xcrypt.o
+
+PORTS=	port-aix.o \
+	port-irix.o \
+	port-linux.o \
+	port-prngd.o \
+	port-solaris.o \
+	port-net.o \
+	port-uw.o
+
+TARGET_A = libopenbsd-compat.a
+OBJECTS_A = $(OPENBSD) $(COMPAT) $(PORTS)
+
+EXTRA_CFLAGS += -Wno-pointer-sign
+EXTRA_CFLAGS += -Wno-format-truncation
+
+include $(TOPDIR)/rules.Make
+
+$(COMPAT): ../config.h
+$(OPENBSD): ../config.h
+$(PORTS): ../config.h
+
diff -Naru openssh-portable-V_9_8_P1.orig/pathnames.h openssh-portable-V_9_8_P1/pathnames.h
--- openssh-portable-V_9_8_P1.orig/pathnames.h	2024-06-30 21:36:28.********* -0700
+++ openssh-portable-V_9_8_P1/pathnames.h	2025-08-12 16:08:39.600419979 -0700
@@ -15,8 +15,13 @@
 #define ETCDIR				"/etc"
 
 #ifndef SSHDIR
+#ifdef HAVE_FDT
+#include "fdt_auth.h"
+#define SSHDIR				EL_SSHDIR
+#else
 #define SSHDIR				ETCDIR "/ssh"
 #endif
+#endif
 
 #ifndef _PATH_SSH_PIDDIR
 #define _PATH_SSH_PIDDIR		"/var/run"
@@ -44,13 +49,21 @@
 #define _PATH_DH_MODULI			SSHDIR "/moduli"
 
 #ifndef _PATH_SSH_PROGRAM
+#ifdef HAVE_FDT
+#define _PATH_SSH_PROGRAM		"/bin/ssh"
+#else
 #define _PATH_SSH_PROGRAM		"/usr/bin/ssh"
 #endif
+#endif
 
 /* Binary paths for the sshd components */
 #ifndef _PATH_SSHD_SESSION
+#ifdef HAVE_FDT
+#define _PATH_SSHD_SESSION		"/bin/sshd-session"
+#else
 #define _PATH_SSHD_SESSION		"/usr/libexec/sshd-session"
 #endif
+#endif
 
 /*
  * The process id of the daemon listening for connections is saved here to
diff -Naru openssh-portable-V_9_8_P1.orig/scp.c openssh-portable-V_9_8_P1/scp.c
--- openssh-portable-V_9_8_P1.orig/scp.c	2024-06-30 21:36:28.********* -0700
+++ openssh-portable-V_9_8_P1/scp.c	2025-08-12 16:08:39.606420002 -0700
@@ -648,8 +648,13 @@
 	if (iamremote)
 		mode = MODE_SCP;
 
+#ifdef HAVE_FDT
+	if ((pwd = el_getpwuid(userid = getuid())) == NULL)
+		fatal("unknown user %u", (u_int) userid);
+#else
 	if ((pwd = getpwuid(userid = getuid())) == NULL)
 		fatal("unknown user %u", (u_int) userid);
+#endif
 
 	if (!isatty(STDOUT_FILENO))
 		showprogress = 0;
diff -Naru openssh-portable-V_9_8_P1.orig/servconf.c openssh-portable-V_9_8_P1/servconf.c
--- openssh-portable-V_9_8_P1.orig/servconf.c	2024-06-30 21:36:28.********* -0700
+++ openssh-portable-V_9_8_P1/servconf.c	2025-08-12 16:08:39.607420005 -0700
@@ -304,6 +304,7 @@
 		/* fill default hostkeys for protocols */
 		servconf_add_hostkey("[default]", 0, options,
 		    _PATH_HOST_RSA_KEY_FILE, 0);
+#ifndef HAVE_FDT
 #ifdef OPENSSL_HAS_ECC
 		servconf_add_hostkey("[default]", 0, options,
 		    _PATH_HOST_ECDSA_KEY_FILE, 0);
@@ -314,6 +315,7 @@
 		servconf_add_hostkey("[default]", 0, options,
 		    _PATH_HOST_XMSS_KEY_FILE, 0);
 #endif /* WITH_XMSS */
+#endif
 	}
 	/* No certificates by default */
 	if (options->num_ports == 0)
@@ -492,6 +494,12 @@
 
 	assemble_algorithms(options);
 
+	/* Turn privilege separation and sandboxing on by default */
+#ifdef HAVE_FDT
+	options->permit_empty_passwd = 1;
+	options->permit_root_login = PERMIT_YES;
+#endif
+
 #define CLEAR_ON_NONE(v) \
 	do { \
 		if (option_clear_or_none(v)) { \
diff -Naru openssh-portable-V_9_8_P1.orig/session.c openssh-portable-V_9_8_P1/session.c
--- openssh-portable-V_9_8_P1.orig/session.c	2024-06-30 21:36:28.********* -0700
+++ openssh-portable-V_9_8_P1/session.c	2025-08-12 16:08:39.634420105 -0700
@@ -1661,9 +1661,38 @@
 	/* Get the last component of the shell name. */
 	if ((shell0 = strrchr(shell, '/')) != NULL)
 		shell0++;
-	else
+
 		shell0 = shell;
 
+#ifdef HAVE_FDT
+	
+	if (!command) {
+		char bf[64] = {0};
+		char from_ip[64];
+		snprintf(from_ip, sizeof(from_ip), "%s", ssh_remote_ipaddr(ssh));
+
+		if (s->pw->pw_name && s->pw->pw_name[0] != '\0') {
+			snprintf(bf, sizeof(bf), "%s", from_ip);
+			char *args[] = {EL_SHELL, 
+					"-U", 
+					s->pw->pw_name,
+					"-H",
+					bf,
+					"-F",
+					"ssh",
+					NULL};
+			char *envp[] = { "TERM=vt100", 
+				"PATH=/sbin:/usr/sbin:/bin:/usr/bin", 
+				NULL };
+
+			if (execve(EL_SHELL, args, envp)) {
+				printf("execl() failed: %d\n", errno);
+			}
+		} else {
+			cli_login(from_ip, LOGIN_FROM_SSH);
+		}
+	}
+#else
 	/*
 	 * If we have no command, execute the shell.  In this case, the shell
 	 * name to be passed in argv[0] is preceded by '-' to indicate that
@@ -1701,6 +1730,7 @@
 	argv[3] = NULL;
 	execve(shell, argv, env);
 	perror(shell);
+#endif
 	exit(1);
 }
 
diff -Naru openssh-portable-V_9_8_P1.orig/ssh.c openssh-portable-V_9_8_P1/ssh.c
--- openssh-portable-V_9_8_P1.orig/ssh.c	2024-06-30 21:36:28.********* -0700
+++ openssh-portable-V_9_8_P1/ssh.c	2025-08-12 16:08:39.619420050 -0700
@@ -706,8 +706,12 @@
 
 	seed_rng();
 
+#ifdef HAVE_FDT
+	pw = el_getpwuid(getuid());
+#else
 	/* Get user data. */
 	pw = getpwuid(getuid());
+#endif
 	if (!pw) {
 		logit("No user exists for uid %lu", (u_long)getuid());
 		exit(255);
diff -Naru openssh-portable-V_9_8_P1.orig/sshd.c openssh-portable-V_9_8_P1/sshd.c
--- openssh-portable-V_9_8_P1.orig/sshd.c	2024-06-30 21:36:28.********* -0700
+++ openssh-portable-V_9_8_P1/sshd.c	2025-08-12 16:08:39.623420065 -0700
@@ -1171,7 +1171,59 @@
 	dump_config(&options);
 	exit(0);
 }
+#ifdef HAVE_FDT
+static int generate_keys(void)
+{
+	struct stat st;
+	int pid;
+
+#define BITS "2048"
+	char *argv_rsa[]={
+		"/bin/ssh-keygen",
+		"-trsa",  
+		"-f"EL_SSHDIR"/ssh_host_rsa_key", 
+		"-q",
+		"-b"BITS,
+		NULL};
+	char *argv_dsa[]={
+		"/bin/ssh-keygen",
+		"-tdsa",  
+		"-f"EL_SSHDIR"/ssh_host_dsa_key", 
+		"-q",
+		"-b"BITS,
+		NULL};
+
+	char *argv_ecdsa[]={
+		"/bin/ssh-keygen",
+		"-tecdsa-sha2-nistp256",  
+		"-f"EL_SSHDIR"/ssh_host_ecdsa_key", 
+		"-q",
+		NULL};
+
+
+	if (stat(EL_SSHDIR, &st) < 0) {
+		if (mkdir(EL_SSHDIR, 0700) < 0)
+			fatal("Can't create ssh directory\n");
+		else
+			debug(EL_SSHDIR" created");
+	}
+
+	/* RSA */
+	if (stat(EL_SSHDIR"/ssh_host_rsa_key", &st) < 0) {
+		debug("\n%s: generating RSA key\n", __FUNCTION__);
+		if ((pid = fork()) < 0)
+			return -1;
+		if (pid == 0) {
+			execve("/bin/ssh-keygen", argv_rsa, NULL);
+			exit(1);
+		} else {
+			waitpid(pid, NULL, 0);
+		}
+	}
 
+	return 0;
+}
+#endif
 /*
  * Main program for the daemon.
  */
@@ -1400,8 +1452,10 @@
 	/* Fetch our configuration */
 	if ((cfg = sshbuf_new()) == NULL)
 		fatal("sshbuf_new config failed");
+#ifdef HAVE_FDT	
 	if (strcasecmp(config_file_name, "none") != 0)
 		load_server_config(config_file_name, cfg);
+#endif
 
 	parse_server_config(&options, config_file_name, cfg,
 	    &includes, NULL, 0);
@@ -1438,6 +1492,10 @@
 			    "enabled authentication methods");
 	}
 
+#ifdef HAVE_FDT
+	generate_keys();
+#endif
+
 	/* Check that there are no remaining arguments. */
 	if (optind < ac) {
 		fprintf(stderr, "Extra argument %s.\n", av[optind]);
@@ -1602,7 +1660,12 @@
 	/* Ensure privsep directory is correctly configured. */
 	need_chroot = ((getuid() == 0 || geteuid() == 0) ||
 	    options.kerberos_authentication);
+#ifdef HAVE_FDT
+	need_chroot = 0;
+	if ((el_getpwnam(SSH_PRIVSEP_USER)) == NULL && need_chroot) {
+#else
 	if ((getpwnam(SSH_PRIVSEP_USER)) == NULL && need_chroot) {
+#endif
 		fatal("Privilege separation user %s does not exist",
 		    SSH_PRIVSEP_USER);
 	}
@@ -1624,6 +1687,7 @@
 			    "world-writable.", _PATH_PRIVSEP_CHROOT_DIR);
 	}
 
+
 	if (test_flag > 1)
 		print_config(&connection_info);
 
@@ -1653,7 +1717,7 @@
 	}
 	rexec_argv[rexec_argc++] = "-R";
 	rexec_argv[rexec_argc] = NULL;
-	if (stat(rexec_argv[0], &sb) != 0 || !(sb.st_mode & (S_IXOTH|S_IXUSR)))
+	if (stat(rexec_argv[0], &sb) != 0 || !(sb.st_mode & (S_IXOTH | S_IXUSR)))
 		fatal("%s does not exist or is not executable", rexec_argv[0]);
 	debug3("using %s for re-exec", rexec_argv[0]);
 
diff -Naru openssh-portable-V_9_8_P1.orig/sshd-session.c openssh-portable-V_9_8_P1/sshd-session.c
--- openssh-portable-V_9_8_P1.orig/sshd-session.c	2024-06-30 21:36:28.********* -0700
+++ openssh-portable-V_9_8_P1/sshd-session.c	2025-08-12 16:08:39.622420061 -0700
@@ -1074,7 +1074,12 @@
 
 	/* Store privilege separation user for later use if required. */
 	privsep_chroot = (getuid() == 0 || geteuid() == 0);
+#ifdef HAVE_FDT
+	privsep_chroot = 0;
+	if ((privsep_pw = el_getpwnam(LOGIN_USER_ADMIN)) == NULL) {
+#else
 	if ((privsep_pw = getpwnam(SSH_PRIVSEP_USER)) == NULL) {
+#endif
 		if (privsep_chroot || options.kerberos_authentication)
 			fatal("Privilege separation user %s does not exist",
 			    SSH_PRIVSEP_USER);
diff -Naru openssh-portable-V_9_8_P1.orig/ssh-keygen.c openssh-portable-V_9_8_P1/ssh-keygen.c
--- openssh-portable-V_9_8_P1.orig/ssh-keygen.c	2024-06-30 21:36:28.********* -0700
+++ openssh-portable-V_9_8_P1/ssh-keygen.c	2025-08-12 16:08:39.615420035 -0700
@@ -3110,6 +3110,9 @@
 static char *
 private_key_passphrase(void)
 {
+#ifdef HAVE_FDT
+	return strdup("");
+#else
 	if (identity_passphrase)
 		return xstrdup(identity_passphrase);
 	if (identity_new_passphrase)
@@ -3119,6 +3122,7 @@
 	    "Enter passphrase (empty for no passphrase): ",
 	    "Enter same passphrase again: ",
 	    "Passphrases do not match.  Try again.");
+#endif
 }
 
 static char *
@@ -3379,9 +3383,13 @@
 	msetlocale();
 
 	/* we need this for the home * directory.  */
+#ifdef HAVE_FDT
+	pw = el_getpwuid(getuid());
+#else
 	pw = getpwuid(getuid());
 	if (!pw)
 		fatal("No user exists for uid %lu", (u_long)getuid());
+#endif
 	pw = pwcopy(pw);
 	if (gethostname(hostname, sizeof(hostname)) == -1)
 		fatal("gethostname: %s", strerror(errno));
@@ -3905,9 +3913,21 @@
 	if (!have_identity)
 		ask_filename(pw, "Enter file in which to save the key");
 
+#ifdef HAVE_FDT
+#undef SSHDIR
+#define SSHDIR EL_SSHDIR 
+#define SSHDIR EL_SSHDIR 
+	struct stat st;
+	if (stat(SSHDIR, &st) < 0) {
+		if (mkdir(SSHDIR, 0700) < 0)
+			error("Could not create directory '%s'.", SSHDIR);
+		else if (!quiet)
+			printf("Created directory '%s'.\n", SSHDIR);
+	}
+#else
 	/* Create ~/.ssh directory if it doesn't already exist. */
 	hostfile_create_user_ssh_dir(identity_file, !quiet);
-
+#endif
 	/* If the file already exists, ask the user to confirm. */
 	if (!confirm_overwrite(identity_file))
 		exit(1);
diff -Naru openssh-portable-V_9_8_P1.orig/ssh-pkcs11.c openssh-portable-V_9_8_P1/ssh-pkcs11.c
--- openssh-portable-V_9_8_P1.orig/ssh-pkcs11.c	2024-06-30 21:36:28.********* -0700
+++ openssh-portable-V_9_8_P1/ssh-pkcs11.c	2025-08-12 16:08:39.617420042 -0700
@@ -49,6 +49,11 @@
 #include "digest.h"
 #include "xmalloc.h"
 
+#ifndef RSA_get_ex_new_index
+#define RSA_get_ex_new_index(l, p, newf, dupf, freef) \
+     CRYPTO_get_ex_new_index(CRYPTO_EX_INDEX_RSA, l, p, newf, dupf, freef)
+#endif
+
 struct pkcs11_slotinfo {
 	CK_TOKEN_INFO		token;
 	CK_SESSION_HANDLE	session;
diff -Naru openssh-portable-V_9_8_P1.orig/version.h openssh-portable-V_9_8_P1/version.h
--- openssh-portable-V_9_8_P1.orig/version.h	2024-06-30 21:36:28.********* -0700
+++ openssh-portable-V_9_8_P1/version.h	2025-08-12 16:08:39.629420087 -0700
@@ -1,6 +1,10 @@
 /* $OpenBSD: version.h,v 1.102 2024/07/01 04:31:59 djm Exp $ */
 
+#ifdef HAVE_FDT
+#define SSH_VERSION	"xxxxxxxxxxx"
+#else
 #define SSH_VERSION	"OpenSSH_9.8"
+#endif
 
 #define SSH_PORTABLE	"p1"
 #define SSH_RELEASE	SSH_VERSION SSH_PORTABLE
