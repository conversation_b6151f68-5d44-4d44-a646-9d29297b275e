--- /Makefile	2022-04-18 04:33:26.000000000 +0000
+++ ../Makefile	2025-01-14 02:01:44.849374202 +0000
@@ -5,8 +5,8 @@
 REGRESS = tlsh_gist
 
 # add include and library paths for both Instant Client and regular Client
-PG_CPPFLAGS = -I/usr/local/include -DBUCKETS_128
-SHLIB_LINK = -L/usr/local/lib/ -ltlsh
+PG_CPPFLAGS = -I/cooked/tlsh_ftnt/include -DBUCKETS_128
+SHLIB_LINK = -L/cooked/tlsh_ftnt/lib -ltlsh_shared
 
 REGRESS = tlsh_gist
 
@@ -16,7 +16,8 @@
 include $(PGXS)
 else
 subdir = contrib/tlsh_gist
-top_builddir = ../..
+top_builddir = /home/<USER>/cooked/packages/postgres-REL_15_10
+top_srcdir = /home/<USER>/cooked/packages/postgres-REL_15_10
 include $(top_builddir)/src/Makefile.global
 include $(top_srcdir)/contrib/contrib-global.mk
-endif
\ No newline at end of file
+endif
 
diff --git a/tlsh_gist.cpp b/tlsh_gist.cpp
index 68cd961..ff2986b 100644
--- a/tlsh_gist.cpp
+++ b/tlsh_gist.cpp
@@ -1,14 +1,14 @@
 #include "tlsh_gist.h"
 #include "tlsh/tlsh.h"
 
-#include "access/reloptions.h"
-#include "access/gist.h"
 #include "access/stratnum.h"
 #include "fmgr.h"
 #include "port/pg_bitutils.h"
 
 extern "C"
 {
+#include <access/reloptions.h>
+#include <access/gist.h>
     PG_FUNCTION_INFO_V1(tlsh_mean);
     PG_FUNCTION_INFO_V1(tlsh_similarity_op);
 
