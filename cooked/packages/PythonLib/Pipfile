[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
annotated-types = "==0.6.0"
blis = "==1.2.0"
catalogue = "==2.0.10"
certifi = "==2024.2.2"
charset-normalizer = "==3.3.2"
click = "==8.1.7"
cloudpathlib = "==0.16.0"
confection = "==0.1.4"
cymem = "==2.0.8"
distlib = "==0.3.8"
filelock = "==3.13.1"
idna = "==3.6"
jinja2 = "==3.1.5"
langcodes = "==3.3.0"
markupsafe = "==2.1.5"
murmurhash = "==1.0.10"
packaging = "==24.0"
phonenumbers = "==8.13.32"
pipenv = "==2023.12.1"
platformdirs = "==4.2.0"
preshed = "==3.0.9"
presidio-analyzer = "==2.2.354"
presidio-anonymizer = "==2.2.354"
pycryptodome = "==3.20.0"
pydantic = "==1.9.0"
pydantic-core = "==2.16.3"
pyyaml = "==6.0.1"
regex = "==2023.12.25"
"repoze.lru" = "==0.7"
requests = "==2.31.0"
requests-file = "==2.0.0"
routes = "==2.5.1"
smart-open = "==6.4.0"
spacy = "==3.8.4"
spacy-legacy = "==3.0.12"
spacy-loggers = "==1.0.5"
srsly = "==2.4.8"
thinc = "==8.3.4"
tldextract = "==5.1.1"
tqdm = "==4.66.2"
typer = "==0.9.0"
typing-extensions = "==4.10.0"
urllib3 = "==2.2.1"
virtualenv = "==20.25.1"
wasabi = "==1.1.2"
weasel = "==0.3.4"
bottleneck = "==1.3.7"
cffi = "==1.16.0"
chardet = "==4.0.0"
cheroot = "==10.0.1"
cherrypy = "==18.8.0"
cryptography = "==42.0.5"
distro = "==1.8.0"
"jaraco.classes" = "==3.2.1"
"jaraco.collections" = ">=3.3.0"
"jaraco.functools" = ">=3.3.0"
"jaraco.text" = "==3.8.1"
joblib = "==1.2.0"
more-itertools = "==10.1.0"
numexpr = "==2.10.2"
numpy = "==1.26.4"
pandas = "==2.2.1"
portend = "==2.7.1"
pycparser = "==2.21"
pyopenssl = "==24.0.0"
python-dateutil = "==2.8.2"
pytz = "==2023.3.post1"
scikit-learn = "==1.3.0"
scipy = "==1.11.4"
simplejson = "==3.17.6"
six = "==1.16.0"
tempora = "==4.1.1"
threadpoolctl = "==2.2.0"
tzdata = "==2023.3"
"zc.lockfile" = "==2.0"
msgspec = "==0.18.6"
psycopg2-binary = "==2.9.9"
py-bcrypt = "==0.4"
matplotlib = "==3.8.3"
dnspython = "==2.6.1"
dpkt = "==1.9.8"
multidict = "==6.0.5"
yarl = "==1.9.4"
aiohttp = "==3.9.3"
aiohttp-jinja2 = "==1.6"
antlr4-python3-runtime = "==4.13.1"
asn1crypto = "==1.5.1"
itsdangerous = "==2.1.2"
multipledispatch = "==1.0.0"
pydatalog = "==0.17.4"
toolz = "==0.12.1"
orjson = "==3.9.15"
unification = "==0.2.2"
email-validator = "==2.1.1"
aiohttp-apispec = "==2.2.3"
aiohttp-security = "==0.5.0"
aiohttp-session = "==2.12.0"
dirhash = "==0.2.1"
donut-shellcode = "==1.0.2"
ldap3 = "==2.9.1"
lxml = "==5.1.0"
marshmallow = "==3.5.1"
marshmallow-enum = "==1.5.1"
reportlab = "==4.1.0"
svglib = "==1.5.1"
websockets = "==12.0"
fasteners = "==0.19"
pyminizip = "==0.2.6"
pika = "==1.3.2"
uncurl = "*"
bson="==0.5.10"
aio-pika = "==9.4.0"
tlv8="==0.10.0"
huey="==2.5.0"
boto3="==1.35.17"
redis="==5.0.3"
python-docx="==1.1.0"
PyPDF2="==3.0.1"
python-pptx="==0.6.23"
openpyxl="==3.1.2"
ezodf="==0.3.2"
pillow="==11.0.0"
pillow-avif-plugin="==1.4.6"
mutagen="==1.47.0"
grpcio="==1.62.1"
aerospike="==14.2.0"
supervisor="==4.2.5"
Flask="==3.0.2"
Flask-Cors="==4.0.0"
flask-blueprint="==1.3.0"
SQLAlchemy="==2.0.29"
psutil="==5.9.8"
APScheduler="==3.10.4"
python-magic="==0.4.27"
pdfplumber="==0.11.0"
pytesseract="==0.3.10"
odfpy="==1.4.1"
striprtf="==0.0.26"
Office365-REST-Python-Client="==2.5.8"
tenacity="==8.2.3"
requests-ntlm="==1.2.0"
hyperscan="==0.7.7"
memory_profiler="==0.61.0"
transformers="==4.40.2"
pysmb="==*******"
xlrd="==2.0.1"
cachetools="==5.3.3"
torch = {version="2.3.0+cpu", file="https://download.pytorch.org/whl/cpu/torch-2.3.0%2Bcpu-cp310-cp310-linux_x86_64.whl"}
datrie="==0.8.2"
"aspose.slides"="==24.5.0"
readerwriterlock="==1.0.9"
pysqlcipher3="==1.2.0"
gevent="==24.2.1"
pdf2image="==1.17.0"
ocrmypdf="==16.4.0"
pyroute2="==0.7.12"
gunicorn="==22.0.0"
concurrent-log-handler="==0.9.25"
intervaltree="==3.1.0"
brotli="==1.0.9"
zstandard="==0.23.0"
pymupdf="==1.24.10"
natsort="==8.4.0"
rfc5424-logging-handler="==1.4.3"
smbprotocol="==1.14.0"
pysmbc="==********"
tifffile="==2024.5.22"
py-tlsh="==4.7.2"
simhash="==2.1.2"
pyee="==13.0.0"
greenlet="==3.2.1"
playwright="==1.52.0"
google-api-python-client="==2.174.0"
google-auth-httplib2="==0.2.0"
google-auth-oauthlib="==1.2.2"
py-spy="==0.4.0"
html2text="*"
pycountry="==24.6.1"

[dev-packages]

[requires]
python_version = "3.10"
