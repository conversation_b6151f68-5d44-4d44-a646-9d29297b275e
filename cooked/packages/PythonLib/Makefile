TOPDIR = ../../..
NO_DEPCLEAN_RULE=y
NO_CLEAN_RULE=y
PYTHON_INSTALL_HOME=$(realpath $(TOPDIR))/cooked
PYTHONHOME=/cooked
PYTHON_INSTALL_PATH=${PYTHON_INSTALL_HOME}/python3/lib/python3.10
PYTHONPATH=/cooked/lib/python3_10.zip:/cooked/lib/python3.10:/cooked/lib/python3.10/lib-dynload
PYTHON_BIN=/cooked/bin/python3.10
WALINUXAGENT=WALinuxAgent-*******

export PYTHONPATH
.PHONY: build_python add_package clean install

all: build_python add_package

build_python:
	@sh build_python.sh

#install pip & setuptools & necessary package
add_package:
	$(PYTHONHOME)/bin/python3.10 -m ensurepip --default-pip || exit 1
	$(PYTHONHOME)/bin/pip3 install pipenv || exit 1
	export PATH=$(PYTHONHOME)/bin/:$$PATH;PIPENV_VENV_IN_PROJECT=1 pipenv install --python 3.10

patch_lib:
	# patch cheroot
	cd .venv/lib/python3.10/site-packages; patch -p0 < ../../../../cheroot-10.0.1.patch; patch -p0 < ../../../../supervisor-UserWarning.patch
waagent:
	@if [ ! -d $(WALINUXAGENT) ]; then		\
		tar -xf $(WALINUXAGENT).tar.gz;		\
		cd $(WALINUXAGENT); patch -p1 < ../$(WALINUXAGENT).patch;	\
		$(PYTHONHOME)/bin/pipenv run python ./setup.py install;		\
	fi;	

clean:
	-rm -rf /cooked
	-rm -rf .venv
	-rm -rf $(WALINUXAGENT)

install: $(PYTHONHOME)/lib/python-3.10.16.libs.tgz patch_lib
	@echo "install python3.10 and packages to $(PYTHON_INSTALL_HOME)/python3"
	-@mkdir -p $(PYTHON_INSTALL_HOME)/python3/
	cp $(PYTHON_BIN) $(PYTHON_INSTALL_HOME)/bin/
	cp $(PYTHONHOME)/lib/python-3.10.16.libs.tgz $(PYTHON_INSTALL_HOME)/python3/
	-rm -rf $(PYTHON_INSTALL_HOME)/python3/lib
	rm .venv/lib/python3.10/site-packages/.gitignore
	cp -r .venv/lib	$(PYTHON_INSTALL_HOME)/python3/
	-@mkdir -p $(PYTHON_INSTALL_HOME)/python3/bin/
	cd .venv/bin; cp cheroot distro huey_consumer huey_consumer.py supervisorctl supervisord gunicorn py-spy $(PYTHON_INSTALL_HOME)/python3/bin
	sed -i "1s|.*|#!/bin/python3|" $(PYTHON_INSTALL_HOME)/python3/bin/gunicorn
	sed -i "1s|.*|#!/bin/python3|" $(PYTHON_INSTALL_HOME)/python3/bin/huey_consumer
	sed -i "1s|.*|#!/bin/python3|" $(PYTHON_INSTALL_HOME)/python3/bin/huey_consumer.py	
	sed -i "1s|.*|#!/bin/python3|" $(PYTHON_INSTALL_HOME)/python3/bin/supervisorctl
	sed -i "1s|.*|#!/bin/python3|" $(PYTHON_INSTALL_HOME)/python3/bin/supervisord
	@find $(PYTHON_INSTALL_PATH) -path "$(PYTHON_INSTALL_PATH)/site-packages/PIL" -prune -o -name "*.so" -exec strip --strip-unneeded {} \;
	@find $(PYTHON_INSTALL_PATH) -name "__pycache__" -type d 2>/dev/null | xargs rm -rf
	
include $(TOPDIR)/rules.Make

#	cd .venv/bin; cp cherryd cheroot distro chardetect f2py3 f2py3.8  $(PYTHON_INSTALL_HOME)/python3/bin 37行之后
