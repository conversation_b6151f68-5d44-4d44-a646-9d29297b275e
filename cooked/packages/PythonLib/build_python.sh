#!/bin/bash
# 
# build_python.sh -- <+description+>
#
# v 1.0, 03/27/2019 15:28:19
# v 1.1, 02/02/2021
#
# Copyright (c) 2018 Fortinet, Inc. All rights reserved.

PYTHON_SRC=Python-3.10.16
# OPENSSL_HOME=../openssl-dpdk
# OPENSSL_VER=openssl-OpenSSL_1_1_1t
PREFIX_DIR=/cooked
INSTALL_TAR_NAME=python-3.10.16.libs.tgz
LANG=C


build_openssl() {
	echo "Start build $OPENSSL_VER .."

	if [ -f  ${PREFIX_DIR}/bin/openssl ]; then
		echo "Openssl already exists in ${PREFIX_DIR}"
		return
	fi
	if [ ! -f $OPENSSL_HOME/${OPENSSL_VER}.tar.gz ]; then
		echo "openssl file is not exists: $OPENSSL_HOME/${OPENSSL_VER}.tar.gz"
		exit 1
	fi
	if [ ! -d $OPENSSL_VER ]; then
		tar xzf $OPENSSL_HOME/${OPENSSL_VER}.tar.gz 
	fi

	cd $OPENSSL_VER
	./config --prefix=${PREFIX_DIR} -Wl,-rpath,${PREFIX_DIR}/lib \
		enable-ssl3 \
		enable-ssl3-method \
		enable-ec_nistp_64_gcc_128 \
		enable-weak-ssl-ciphers \
		enable-rc5 \
		enable-md2 \
		enable-deprecated \
		-O3 -pthread -DOPENSSL_THREADS -D_REENTRANT -D_THREAD_SAFE \
		-DOPENSSL_API_COMPAT=0x00908000L
	make || exit 1
	make install 

	cd ..
	rm -rf $OPENSSL_VER
	echo "Done."
}

#create tar file used by install before install other python package.
create_install_tar()
{
	#strip and remove pycache
	strip --strip-unneeded ${PREFIX_DIR}/bin/python3.10
	find ${PREFIX_DIR}/lib/python3.10/ -name "*.so" -exec strip --strip-unneeded {} \;
	find ${PREFIX_DIR}/lib/python3.10/ -name "__pycache__" -type d |xargs rm -rf

	#create tar file used by install before install other python package.
	cd ${PREFIX_DIR}/lib/
	rm -f ${INSTALL_TAR_NAME}
	tar -czf ${INSTALL_TAR_NAME} python3.10 || exit 1
}

if [ -f ${PREFIX_DIR}/bin/python3.10 ]; then
	echo "python3.10 interpreter already exists in ${PREFIX_DIR}"
	[ -f "$INSTALL_TAR_NAME" ] || create_install_tar
	exit 0
fi
'''
if ! dpkg -l | grep "libbz2-dev" > /dev/null;then
	echo "Install libbz2-dev"
	#Remove proxy config in apt.conf on buildenv; 
	[ -f "/etc/apt/apt.conf" ] && sed -i '/192.168/d' /etc/apt/apt.conf
	#install libbz2-dev via apt-get
	apt-get install -y libbz2-dev || exit 1
fi

if ! dpkg -l | grep "libffi-dev.*3.1~rc1+r3.0.13-12" > /dev/null;then
	echo "Install libffi-dev 3.1~rc1"
	dpkg -i --force-depends libffi-dev_3.1_rc1+r3.0.13-12_amd64.deb || exit 1
fi
'''
#build_openssl

export PATH=${PREFIX_DIR}/bin:$PATH

if [ ! -d $PYTHON_SRC ]; then
	tar xf $PYTHON_SRC.tgz
	cd $PYTHON_SRC;
	patch -p1 < ../$PYTHON_SRC.patch
else
	cd $PYTHON_SRC
fi

CC='gcc' \
	CFLAGS='-g -fstack-protector --param=ssp-buffer-size=4  -Wformat -Werror=format-security' \
	LDFLAGS='-Wl,-Bsymbolic-functions -Wl,-z,relro -Wl,-R/cooked/lib' \
	CPPFLAGS='-D_FORTIFY_SOURCE=2'  \
	ac_cv_posix_semaphores_enabled=yes ./configure --prefix=${PREFIX_DIR} \
		--enable-ipv6 --enable-loadable-sqlite-extensions --with-dbmliborder=bdb:gdbm --with-computed-gotos --without-ensurepip --with-system-expat --with-system-libmpdec --with-system-ffi || exit 1

make || exit 1

make install || exit 1

cd ../

#create tar file used by install before install other python package.
create_install_tar



rm -rf $PYTHON_SRC

