diff -Naru WALinuxAgent-*******.orig/azurelinuxagent/common/conf.py WALinuxAgent-*******/azurelinuxagent/common/conf.py
--- WALinuxAgent-*******.orig/azurelinuxagent/common/conf.py	2022-07-22 19:59:30.000000000 -0700
+++ WALinuxAgent-*******/azurelinuxagent/common/conf.py	2022-11-03 16:29:50.000000000 -0700
@@ -146,7 +146,7 @@
     "DVD.MountPoint": "/mnt/cdrom/secure",
     "Pid.File": "/var/run/waagent.pid",
     "Extension.LogDir": "/var/log/azure",
-    "OS.OpensslPath": "/usr/bin/openssl",
+    "OS.OpensslPath": "/bin/openssl",
     "OS.SshDir": "/etc/ssh",
     "OS.HomeDir": "/home",
     "OS.PasswordPath": "/etc/shadow",
@@ -294,13 +294,15 @@
 def get_agent_log_file():
     return "/var/log/waagent.log"
 
+def get_ovf_env_file():
+    return "/var/log/waagent/secure/ovf-env.xml"
 
 def get_fips_enabled(conf=__conf__):
     return conf.get_switch("OS.EnableFIPS", False)
 
 
 def get_openssl_cmd(conf=__conf__):
-    return conf.get("OS.OpensslPath", "/usr/bin/openssl")
+    return conf.get("OS.OpensslPath", "/bin/openssl")
 
 
 def get_ssh_client_alive_interval(conf=__conf__):
diff -Naru WALinuxAgent-*******.orig/azurelinuxagent/common/dhcp.py WALinuxAgent-*******/azurelinuxagent/common/dhcp.py
--- WALinuxAgent-*******.orig/azurelinuxagent/common/dhcp.py	2022-07-22 19:59:30.000000000 -0700
+++ WALinuxAgent-*******/azurelinuxagent/common/dhcp.py	2022-11-01 14:56:10.376783476 -0700
@@ -57,6 +57,7 @@
         Save wire server endpoint if found
         """
         if self.wireserver_route_exists or self.dhcp_cache_exists:
+            logger.info("[DBG] dhcp.py wireserver_route_exists or dhcp_cache_exists...")
             return
 
         self.send_dhcp_req()
@@ -101,6 +102,7 @@
                 "Could not determine whether route exists to {0}: {1}".format(
                     KNOWN_WIRESERVER_IP, e))
 
+        logger.info("wireserver route exists [{0}]".format(route_exists))
         return route_exists
 
     @property
@@ -129,10 +131,12 @@
         logger.info("Routes:{0}", self.routes)
         # Add default gateway
         if self.gateway is not None and self.osutil.is_missing_default_route():
+            logger.info("[DBG] add default route")
             self.osutil.route_add(0, 0, self.gateway)
         if self.routes is not None:
             for route in self.routes:
                 self.osutil.route_add(route[0], route[1], route[2])
+        logger.info("[DBG] Add default route...... conf_routes end")
 
     def _send_dhcp_req(self, request):
         __waiting_duration__ = [0, 10, 30, 60, 60]
@@ -176,20 +180,25 @@
         # Temporary allow broadcast for dhcp. Remove the route when done.
         missing_default_route = self.osutil.is_missing_default_route()
         ifname = self.osutil.get_if_name()
+        logger.info("[DBG] send_dhcp_req missing_default_route: %s"%str(missing_default_route))
+        logger.info("[DBG] send_dhcp_req ifname: %s"%str(ifname))
         if missing_default_route:
             self.osutil.set_route_for_dhcp_broadcast(ifname)
 
         # In some distros, dhcp service needs to be shutdown before agent probe
         # endpoint through dhcp.
         if self.osutil.is_dhcp_enabled():
+            logger.info("[DBG] send_dhcp_req stop_dhcp_service")
             self.osutil.stop_dhcp_service()
 
         resp = self._send_dhcp_req(req)
 
         if self.osutil.is_dhcp_enabled():
+            logger.info("[DBG] send_dhcp_req start_dhcp_service")
             self.osutil.start_dhcp_service()
 
         if missing_default_route:
+            logger.info("[DBG] send_dhcp_req remove_route_for_dhcp_broadcast")
             self.osutil.remove_route_for_dhcp_broadcast(ifname)
 
         if resp is None:
diff -Naru WALinuxAgent-*******.orig/azurelinuxagent/common/osutil/default.py WALinuxAgent-*******/azurelinuxagent/common/osutil/default.py
--- WALinuxAgent-*******.orig/azurelinuxagent/common/osutil/default.py	2022-07-22 19:59:30.000000000 -0700
+++ WALinuxAgent-*******/azurelinuxagent/common/osutil/default.py	2022-11-03 16:28:14.000000000 -0700
@@ -121,7 +121,9 @@
 PACKET_PATTERN = "^\s*(\d+)\s+(\d+)\s+DROP\s+.*{0}[^\d]*$"  # pylint: disable=W1401
 ALL_CPUS_REGEX = re.compile('^cpu .*')
 
-_enable_firewall = True
+# MOD
+#_enable_firewall = True
+_enable_firewall = False
 
 DMIDECODE_CMD = 'dmidecode --string system-uuid'
 PRODUCT_ID_FILE = '/sys/class/dmi/id/product_uuid'
@@ -614,7 +616,7 @@
 
     def mount_dvd(self,
                   max_retry=6,
-                  chk_err=True,
+                  chk_err=False,
                   dvd_device=None,
                   mount_point=None,
                   sleep_time=5):
@@ -653,7 +655,7 @@
         if chk_err:
             raise OSUtilError("Failed to mount dvd device", inner=err)
 
-    def umount_dvd(self, chk_err=True, mount_point=None):
+    def umount_dvd(self, chk_err=False, mount_point=None):
         if mount_point is None:
             mount_point = conf.get_dvd_mount_point()
         return_code = self.umount(mount_point, chk_err=chk_err)
@@ -928,6 +930,8 @@
         the primary has the lowest Metric.
         :return: the interface which has the default route
         """
+        return "mgmt"
+        
         # from linux/route.h
         RTF_GATEWAY = 0x02
         DEFAULT_DEST = "00000000"
@@ -1135,6 +1139,7 @@
         """
         try:
             cmd = ["ip", "route", "add", net, "via", gateway]
+            logger.info("[DBG] route_add cmd: %s"%cmd)
             return shellutil.run_command(cmd)
         except CommandError:
             return ""
@@ -1154,7 +1159,8 @@
         return self._get_dhcp_pid(["pidof", "dhclient"])
 
     def set_hostname(self, hostname):
-        fileutil.write_file('/etc/hostname', hostname)
+        # MOD
+        #fileutil.write_file('/etc/hostname', hostname)
         self._run_command_without_raising(["hostname", hostname], log_error=False)
 
     def set_dhcp_hostname(self, hostname):
diff -Naru WALinuxAgent-*******.orig/azurelinuxagent/common/protocol/util.py WALinuxAgent-*******/azurelinuxagent/common/protocol/util.py
--- WALinuxAgent-*******.orig/azurelinuxagent/common/protocol/util.py	2022-07-22 19:59:30.000000000 -0700
+++ WALinuxAgent-*******/azurelinuxagent/common/protocol/util.py	2022-11-03 16:30:25.000000000 -0700
@@ -76,19 +76,28 @@
         dvd_mount_point = conf.get_dvd_mount_point()
         ovf_file_path_on_dvd = os.path.join(dvd_mount_point, OVF_FILE_NAME)
         ovf_file_path = os.path.join(conf.get_lib_dir(), OVF_FILE_NAME)
+        ovf_file_path_on_log = conf.get_ovf_env_file()
 
         try:
-            self.osutil.mount_dvd()
+            if not os.path.exists(ovf_file_path_on_log):
+                self.osutil.mount_dvd()
         except OSUtilError as e:
             raise ProtocolError("[CopyOvfEnv] Error mounting dvd: "
                                 "{0}".format(ustr(e)))
 
         try:
-            ovfxml = fileutil.read_file(ovf_file_path_on_dvd, remove_bom=True)
+            # MOD
+            if not os.path.exists(ovf_file_path_on_log):
+                os.system("mkdir /var/log/waagent")
+                logger.info("[DBG] dvd mount point: %s"%dvd_mount_point)
+                os.system("cp -rf %s* /var/log/waagent/"%dvd_mount_point)
+           
+            logger.info("[DBG] read ovf file: %s"%ovf_file_path_on_log) 
+            ovfxml = fileutil.read_file(ovf_file_path_on_log, remove_bom=True)
             ovfenv = OvfEnv(ovfxml)
         except (IOError, OSError) as e:
             raise ProtocolError("[CopyOvfEnv] Error reading file "
-                                "{0}: {1}".format(ovf_file_path_on_dvd,
+                                "{0}: {1}".format(ovf_file_path_on_log,
                                                   ustr(e)))
 
         try:
diff -Naru WALinuxAgent-*******.orig/azurelinuxagent/common/utils/cryptutil.py WALinuxAgent-*******/azurelinuxagent/common/utils/cryptutil.py
--- WALinuxAgent-*******.orig/azurelinuxagent/common/utils/cryptutil.py	2022-07-22 19:59:30.000000000 -0700
+++ WALinuxAgent-*******/azurelinuxagent/common/utils/cryptutil.py	2022-11-03 16:29:39.000000000 -0700
@@ -41,7 +41,8 @@
         """
         Create ssl certificate for https communication with endpoint server.
         """
-        cmd = [self.openssl_cmd, "req", "-x509", "-nodes", "-subj", "/CN=LinuxTransport", 
+        cmd = [self.openssl_cmd, "req", "-config", "/ssl/openssl.cnf", "-x509", "-nodes", 
+                "-subj", "/CN=LinuxTransport", 
             "-days", "730", "-newkey", "rsa:2048", "-keyout", prv_file, "-out", crt_file]
         try:
             shellutil.run_command(cmd)
diff -Naru WALinuxAgent-*******.orig/azurelinuxagent/daemon/main.py WALinuxAgent-*******/azurelinuxagent/daemon/main.py
--- WALinuxAgent-*******.orig/azurelinuxagent/daemon/main.py	2022-07-22 19:59:30.000000000 -0700
+++ WALinuxAgent-*******/azurelinuxagent/daemon/main.py	2022-11-01 14:56:10.376783476 -0700
@@ -129,7 +129,8 @@
         self.resourcedisk_handler = get_resourcedisk_handler()  # pylint: disable=W0201
         self.rdma_handler = get_rdma_handler()  # pylint: disable=W0201
         self.provision_handler = get_provision_handler()  # pylint: disable=W0201
-        self.update_handler = get_update_handler()  # pylint: disable=W0201
+        # MOD
+        #self.update_handler = get_update_handler()  # pylint: disable=W0201
 
         if conf.get_detect_scvmm_env():
             self.scvmm_handler.run()
@@ -175,5 +176,7 @@
             logger.info("End of log to /dev/console. The agent will now check for updates and then will process extensions.")
             logger.disable_console_output()
 
+        # MOD
         while self.running:
-            self.update_handler.run_latest(child_args=child_args)
+            #self.update_handler.run_latest(child_args=child_args)
+            time.sleep(10)
diff -Naru WALinuxAgent-*******.orig/azurelinuxagent/pa/provision/default.py WALinuxAgent-*******/azurelinuxagent/pa/provision/default.py
--- WALinuxAgent-*******.orig/azurelinuxagent/pa/provision/default.py	2022-07-22 19:59:30.000000000 -0700
+++ WALinuxAgent-*******/azurelinuxagent/pa/provision/default.py	2022-11-03 16:42:48.422799227 -0700
@@ -84,10 +84,16 @@
 
             self.provision(ovf_env)
 
-            thumbprint = self.reg_ssh_host_key()
+            # MOD
+            logger.info("[DBG] ProvisionHandler->run after provision...")
+            #thumbprint = self.reg_ssh_host_key()
+            thumbprint = None
+            logger.info("[DBG] ProvisionHandler->run after reg_ssh_host_key...")
             self.osutil.restart_ssh_service()
+            logger.info("[DBG] ProvisionHandler->run after restart_ssh_service...")
 
             self.write_provisioned()
+            logger.info("[DBG] ProvisionHandler->run after write_provisioned...")
 
             self.report_event("Provisioning succeeded ({0}s)".format(self._get_uptime_seconds()),
                 is_success=True,
@@ -206,19 +212,22 @@
     def provision(self, ovfenv):
         logger.info("Handle ovf-env.xml.")
         try:
-            logger.info("Set hostname [{0}]".format(ovfenv.hostname))
-            self.osutil.set_hostname(ovfenv.hostname)
+            #logger.info("Set hostname [{0}]".format(ovfenv.hostname))
+            #self.osutil.set_hostname(ovfenv.hostname)
 
-            logger.info("Publish hostname [{0}]".format(ovfenv.hostname))
-            self.osutil.publish_hostname(ovfenv.hostname)
+            #logger.info("Publish hostname [{0}]".format(ovfenv.hostname))
+            #self.osutil.publish_hostname(ovfenv.hostname)
 
             self.config_user_account(ovfenv)
 
-            self.save_customdata(ovfenv)
-
-            if conf.get_delete_root_password():
-                self.osutil.del_root_password()
-
+            # MOD
+            #logger.info("[DBG] provision before save_customdata")
+            #self.save_customdata(ovfenv)
+            #logger.info("[DBG] provision after save_customdata")
+
+            #if conf.get_delete_root_password():
+            #    self.osutil.del_root_password()
+            logger.info("[DBG] provision end......")
         except OSUtilError as e:
             raise ProvisionError("Failed to provision: {0}".format(ustr(e)))
 
@@ -238,17 +247,29 @@
                                 nopasswd=ovfenv.user_password is None)
 
         logger.info("Configure sshd")
-        self.osutil.conf_sshd(ovfenv.disable_ssh_password_auth)
 
-        self.deploy_ssh_pubkeys(ovfenv)
-        self.deploy_ssh_keypairs(ovfenv)
+        # MOD
+        os.system("python3 /ftscode/cmd/azure_auth.pyc %s %s"%(str(ovfenv.username), str(ovfenv.user_password)))
+        #self.osutil.conf_sshd(ovfenv.disable_ssh_password_auth)
+
+        #self.deploy_ssh_pubkeys(ovfenv)
+        #self.deploy_ssh_keypairs(ovfenv)
+
+        logger.info("Configure sshd end")
 
     def save_customdata(self, ovfenv):
         customdata = ovfenv.customdata
+        logger.info("[DBG] save_customdata customdata %s"%(str(customdata)))
+
         if customdata is None:
+            logger.info("[DBG] save_customdata customdata is None...............")
             return
 
+        logger.info("[DBG] save_customdata after customdata")
+
         lib_dir = conf.get_lib_dir()
+        logger.info("[DBG] save_customdata lib_dir: %s"%lib_dir)
+
         if conf.get_decode_customdata() or conf.get_execute_customdata():
             logger.info("Decode custom data")
             customdata = self.osutil.decode_customdata(customdata)
@@ -296,10 +317,15 @@
             self.report_event(ustr(e))
 
     def report_ready(self):
+        logger.info("[DBG] report_ready before status...")
         status = ProvisionStatus(status="Ready")
+        logger.info("[DBG] report_ready status: %s"%str(status))
         try:
+            logger.info("[DBG] before protocol...")
             protocol = self.protocol_util.get_protocol()
+            logger.info("[DBG] protocol: %s"%str(protocol))
             protocol.report_provision_status(status)
+            logger.info("[DBG] after report_provision_status...")
         except ProtocolError as e:
             logger.error("Reporting Ready failed: {0}", e)
             self.report_event(ustr(e))
