--- supervisor/options.py	2025-06-04 15:14:50.755224842 -0700
+++ supervisor/options_fix.py	2025-06-04 16:01:30.800212122 -0700
@@ -10,7 +10,7 @@
 import grp
 import resource
 import stat
-import pkg_resources
+import importlib
 import glob
 import platform
 import warnings
@@ -387,14 +387,13 @@
         return factories
 
     def import_spec(self, spec):
-        ep = pkg_resources.EntryPoint.parse("x=" + spec)
-        if hasattr(ep, 'resolve'):
-            # this is available on setuptools >= 10.2
-            return ep.resolve()
-        else:
-            # this causes a DeprecationWarning on setuptools >= 11.3
-            return ep.load(False)
-
+        module_name, _, attr = spec.partition(":")
+        try:
+            module = importlib.import_module(module_name)
+            result = getattr(module, attr)
+            return result
+        except ImportError as e:
+            raise ImportError(f"Can not import '{spec}'") from e
 
 class ServerOptions(Options):
     user = None
