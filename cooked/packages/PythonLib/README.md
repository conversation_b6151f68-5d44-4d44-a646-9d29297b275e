##Desctiption
The content in this directory is used to generate the pythonLib;
Include the python interpreter and nessary third-party packages.

Overall process refers to the "PythonLib_Readme.jpg" file.
1) Build python3.8.16 to the temporary directory "/cooked" and install necessary packages via pipenv
   This step is completed by the "make" command.
2) Install interpreter and packages to cooked/python3 directory that will be used when make image.  
3) The script/mkimg.sh script will put the necessary files from cooked/python3 directory into image.

1 and 2 are executed only when there is content update; 
it will not be executed every time the make image

##Build:
When need to upgrade or add new python packages, follow the setps below:
1) Modify the Pipfile file and add packages that need to be added.
For example:
yarl = "*" 
or
yarl = "==1.6.3"

2) Build and install
cd cooked/packages/PythonLib
make
make install

Needs to be excuted in buildenv, currently verifying the buildevn version is "fts_chroot_v2.8.1"

"make" will generate two directories:
/cooked #Contains python3.8 interpreter and standard library and openssl.
.venv  #python vm; include nessary third-party packages.

make install will add contents to the following location:
1) Add python3.8 binary to "cooked/bin".
2) Add python3.8 standard library to "cooked/python3/python-3.8.libs.tgz"
3) Add hird-party packages binary and lib to "cooked/python3/bin" and "cooked/python3/lib".

##Apply
1) make image:
mkimg.sh will use the installed content, no network support is required.

2) Development or debugging environment:
Copy Pipfile and Pipfile.lock to the project directory in the development or test environment,
and execute:
$pipenv sync
Use a python command in the virtualenv:
$pipenv run python3 xxxx
or spawns a shell within the virtualenv:
$pipenv shell

3) Use in buildenv:
cd cooked/packages/PythonLib
make build_python
export PATH=/cooked/bin:$PATH
python3.8 -m ensurepip --default-pip
pip3 install pipenv
pipenv sync
pipenv shell

