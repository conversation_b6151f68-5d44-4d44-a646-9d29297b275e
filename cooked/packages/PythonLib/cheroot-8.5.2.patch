diff -c cheroot_orig/server.py cheroot/server.py
*** cheroot_orig/server.py	<PERSON><PERSON> Jul 13 10:09:35 2021
--- cheroot/server.py	Thu Jul 22 03:25:35 2021
***************
*** 1000,1005 ****
--- 1000,1008 ----
              # Both server and client are HTTP/1.1
              if self.inheaders.get(b'Connection', b'') == b'close':
                  self.close_connection = True
+                 # for attck-agent api not support keep-alive
+                 if self.path and self.path.find(b'/agent/') != -1:
+                     self.close_connection = False
          else:
              # Either the server or client (or both) are HTTP/1.0
              if self.inheaders.get(b'Connection', b'') != b'Keep-Alive':
***************
*** 1811,1820 ****
              except (KeyboardInterrupt, SystemExit):
                  raise
              except Exception:
!                 self.error_log(
!                     'Error in HTTPServer.serve', level=logging.ERROR,
!                     traceback=True,
!                 )
  
          # raise exceptions reported by any worker threads,
          # such that the exception is raised from the serve() thread.
--- 1814,1824 ----
              except (KeyboardInterrupt, SystemExit):
                  raise
              except Exception:
!                 # self.error_log(
!                 #     'Error in HTTPServer.serve', level=logging.ERROR,
!                 #     traceback=True,
!                 # )
!                 pass
  
          # raise exceptions reported by any worker threads,
          # such that the exception is raised from the serve() thread.
