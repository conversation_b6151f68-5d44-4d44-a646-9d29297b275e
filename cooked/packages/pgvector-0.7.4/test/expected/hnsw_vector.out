SET enable_seqscan = off;
-- L2
CREATE TABLE t (val vector(3));
INSERT INTO t (val) VALUES ('[0,0,0]'), ('[1,2,3]'), ('[1,1,1]'), (NULL);
CREATE INDEX ON t USING hnsw (val vector_l2_ops);
INSERT INTO t (val) VALUES ('[1,2,4]');
SELECT * FROM t ORDER BY val <-> '[3,3,3]';
   val   
---------
 [1,2,3]
 [1,2,4]
 [1,1,1]
 [0,0,0]
(4 rows)

SELECT COUNT(*) FROM (SELECT * FROM t ORDER BY val <-> (SELECT NULL::vector)) t2;
 count 
-------
     4
(1 row)

SELECT COUNT(*) FROM t;
 count 
-------
     5
(1 row)

TRUNCATE t;
SELECT * FROM t ORDER BY val <-> '[3,3,3]';
 val 
-----
(0 rows)

DROP TABLE t;
-- inner product
CREATE TABLE t (val vector(3));
INSERT INTO t (val) VALUES ('[0,0,0]'), ('[1,2,3]'), ('[1,1,1]'), (NULL);
CREATE INDEX ON t USING hnsw (val vector_ip_ops);
INSERT INTO t (val) VALUES ('[1,2,4]');
SELECT * FROM t ORDER BY val <#> '[3,3,3]';
   val   
---------
 [1,2,4]
 [1,2,3]
 [1,1,1]
 [0,0,0]
(4 rows)

SELECT COUNT(*) FROM (SELECT * FROM t ORDER BY val <#> (SELECT NULL::vector)) t2;
 count 
-------
     4
(1 row)

DROP TABLE t;
-- cosine
CREATE TABLE t (val vector(3));
INSERT INTO t (val) VALUES ('[0,0,0]'), ('[1,2,3]'), ('[1,1,1]'), (NULL);
CREATE INDEX ON t USING hnsw (val vector_cosine_ops);
INSERT INTO t (val) VALUES ('[1,2,4]');
SELECT * FROM t ORDER BY val <=> '[3,3,3]';
   val   
---------
 [1,1,1]
 [1,2,3]
 [1,2,4]
(3 rows)

SELECT COUNT(*) FROM (SELECT * FROM t ORDER BY val <=> '[0,0,0]') t2;
 count 
-------
     3
(1 row)

SELECT COUNT(*) FROM (SELECT * FROM t ORDER BY val <=> (SELECT NULL::vector)) t2;
 count 
-------
     3
(1 row)

DROP TABLE t;
-- L1
CREATE TABLE t (val vector(3));
INSERT INTO t (val) VALUES ('[0,0,0]'), ('[1,2,3]'), ('[1,1,1]'), (NULL);
CREATE INDEX ON t USING hnsw (val vector_l1_ops);
INSERT INTO t (val) VALUES ('[1,2,4]');
SELECT * FROM t ORDER BY val <+> '[3,3,3]';
   val   
---------
 [1,2,3]
 [1,2,4]
 [1,1,1]
 [0,0,0]
(4 rows)

SELECT COUNT(*) FROM (SELECT * FROM t ORDER BY val <+> (SELECT NULL::vector)) t2;
 count 
-------
     4
(1 row)

DROP TABLE t;
-- unlogged
CREATE UNLOGGED TABLE t (val vector(3));
INSERT INTO t (val) VALUES ('[0,0,0]'), ('[1,2,3]'), ('[1,1,1]'), (NULL);
CREATE INDEX ON t USING hnsw (val vector_l2_ops);
SELECT * FROM t ORDER BY val <-> '[3,3,3]';
   val   
---------
 [1,2,3]
 [1,1,1]
 [0,0,0]
(3 rows)

DROP TABLE t;
-- options
CREATE TABLE t (val vector(3));
CREATE INDEX ON t USING hnsw (val vector_l2_ops) WITH (m = 1);
ERROR:  value 1 out of bounds for option "m"
DETAIL:  Valid values are between "2" and "100".
CREATE INDEX ON t USING hnsw (val vector_l2_ops) WITH (m = 101);
ERROR:  value 101 out of bounds for option "m"
DETAIL:  Valid values are between "2" and "100".
CREATE INDEX ON t USING hnsw (val vector_l2_ops) WITH (ef_construction = 3);
ERROR:  value 3 out of bounds for option "ef_construction"
DETAIL:  Valid values are between "4" and "1000".
CREATE INDEX ON t USING hnsw (val vector_l2_ops) WITH (ef_construction = 1001);
ERROR:  value 1001 out of bounds for option "ef_construction"
DETAIL:  Valid values are between "4" and "1000".
CREATE INDEX ON t USING hnsw (val vector_l2_ops) WITH (m = 16, ef_construction = 31);
ERROR:  ef_construction must be greater than or equal to 2 * m
SHOW hnsw.ef_search;
 hnsw.ef_search 
----------------
 40
(1 row)

SET hnsw.ef_search = 0;
ERROR:  0 is outside the valid range for parameter "hnsw.ef_search" (1 .. 1000)
SET hnsw.ef_search = 1001;
ERROR:  1001 is outside the valid range for parameter "hnsw.ef_search" (1 .. 1000)
DROP TABLE t;
