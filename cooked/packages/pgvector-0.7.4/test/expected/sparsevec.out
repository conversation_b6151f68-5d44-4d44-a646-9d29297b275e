SELECT '{1:1.5,3:3.5}/5'::sparsevec;
    sparsevec    
-----------------
 {1:1.5,3:3.5}/5
(1 row)

SELECT '{1:-2,3:-4}/5'::sparsevec;
   sparsevec   
---------------
 {1:-2,3:-4}/5
(1 row)

SELECT '{1:2.,3:4.}/5'::sparsevec;
  sparsevec  
-------------
 {1:2,3:4}/5
(1 row)

SELECT ' { 1 : 1.5 ,  3  :  3.5  } / 5 '::sparsevec;
    sparsevec    
-----------------
 {1:1.5,3:3.5}/5
(1 row)

SELECT '{1:1.23456}/1'::sparsevec;
   sparsevec   
---------------
 {1:1.23456}/1
(1 row)

SELECT '{1:hello,2:1}/2'::sparsevec;
ERROR:  invalid input syntax for type sparsevec: "{1:hello,2:1}/2"
LINE 1: SELECT '{1:hello,2:1}/2'::sparsevec;
               ^
SELECT '{1:NaN,2:1}/2'::sparsevec;
ERROR:  NaN not allowed in sparsevec
LINE 1: SELECT '{1:NaN,2:1}/2'::sparsevec;
               ^
SELECT '{1:Infinity,2:1}/2'::sparsevec;
ERROR:  infinite value not allowed in sparsevec
LINE 1: SELECT '{1:Infinity,2:1}/2'::sparsevec;
               ^
SELECT '{1:-Infinity,2:1}/2'::sparsevec;
ERROR:  infinite value not allowed in sparsevec
LINE 1: SELECT '{1:-Infinity,2:1}/2'::sparsevec;
               ^
SELECT '{1:1.5e38,2:-1.5e38}/2'::sparsevec;
        sparsevec         
--------------------------
 {1:1.5e+38,2:-1.5e+38}/2
(1 row)

SELECT '{1:1.5e+38,2:-1.5e+38}/2'::sparsevec;
        sparsevec         
--------------------------
 {1:1.5e+38,2:-1.5e+38}/2
(1 row)

SELECT '{1:1.5e-38,2:-1.5e-38}/2'::sparsevec;
        sparsevec         
--------------------------
 {1:1.5e-38,2:-1.5e-38}/2
(1 row)

SELECT '{1:4e38,2:1}/2'::sparsevec;
ERROR:  "4e38" is out of range for type sparsevec
LINE 1: SELECT '{1:4e38,2:1}/2'::sparsevec;
               ^
SELECT '{1:-4e38,2:1}/2'::sparsevec;
ERROR:  "-4e38" is out of range for type sparsevec
LINE 1: SELECT '{1:-4e38,2:1}/2'::sparsevec;
               ^
SELECT '{1:1e-46,2:1}/2'::sparsevec;
ERROR:  "1e-46" is out of range for type sparsevec
LINE 1: SELECT '{1:1e-46,2:1}/2'::sparsevec;
               ^
SELECT '{1:-1e-46,2:1}/2'::sparsevec;
ERROR:  "-1e-46" is out of range for type sparsevec
LINE 1: SELECT '{1:-1e-46,2:1}/2'::sparsevec;
               ^
SELECT ''::sparsevec;
ERROR:  invalid input syntax for type sparsevec: ""
LINE 1: SELECT ''::sparsevec;
               ^
DETAIL:  Vector contents must start with "{".
SELECT '{'::sparsevec;
ERROR:  invalid input syntax for type sparsevec: "{"
LINE 1: SELECT '{'::sparsevec;
               ^
SELECT '{ '::sparsevec;
ERROR:  invalid input syntax for type sparsevec: "{ "
LINE 1: SELECT '{ '::sparsevec;
               ^
SELECT '{:'::sparsevec;
ERROR:  invalid input syntax for type sparsevec: "{:"
LINE 1: SELECT '{:'::sparsevec;
               ^
SELECT '{,'::sparsevec;
ERROR:  invalid input syntax for type sparsevec: "{,"
LINE 1: SELECT '{,'::sparsevec;
               ^
SELECT '{}'::sparsevec;
ERROR:  invalid input syntax for type sparsevec: "{}"
LINE 1: SELECT '{}'::sparsevec;
               ^
DETAIL:  Unexpected end of input.
SELECT '{}/'::sparsevec;
ERROR:  invalid input syntax for type sparsevec: "{}/"
LINE 1: SELECT '{}/'::sparsevec;
               ^
SELECT '{}/1'::sparsevec;
 sparsevec 
-----------
 {}/1
(1 row)

SELECT '{}/1a'::sparsevec;
ERROR:  invalid input syntax for type sparsevec: "{}/1a"
LINE 1: SELECT '{}/1a'::sparsevec;
               ^
DETAIL:  Junk after closing.
SELECT '{ }/1'::sparsevec;
 sparsevec 
-----------
 {}/1
(1 row)

SELECT '{:}/1'::sparsevec;
ERROR:  invalid input syntax for type sparsevec: "{:}/1"
LINE 1: SELECT '{:}/1'::sparsevec;
               ^
SELECT '{,}/1'::sparsevec;
ERROR:  invalid input syntax for type sparsevec: "{,}/1"
LINE 1: SELECT '{,}/1'::sparsevec;
               ^
SELECT '{1,}/1'::sparsevec;
ERROR:  invalid input syntax for type sparsevec: "{1,}/1"
LINE 1: SELECT '{1,}/1'::sparsevec;
               ^
SELECT '{:1}/1'::sparsevec;
ERROR:  invalid input syntax for type sparsevec: "{:1}/1"
LINE 1: SELECT '{:1}/1'::sparsevec;
               ^
SELECT '{1:}/1'::sparsevec;
ERROR:  invalid input syntax for type sparsevec: "{1:}/1"
LINE 1: SELECT '{1:}/1'::sparsevec;
               ^
SELECT '{1a:1}/1'::sparsevec;
ERROR:  invalid input syntax for type sparsevec: "{1a:1}/1"
LINE 1: SELECT '{1a:1}/1'::sparsevec;
               ^
SELECT '{1:1a}/1'::sparsevec;
ERROR:  invalid input syntax for type sparsevec: "{1:1a}/1"
LINE 1: SELECT '{1:1a}/1'::sparsevec;
               ^
SELECT '{1:1,}/1'::sparsevec;
ERROR:  invalid input syntax for type sparsevec: "{1:1,}/1"
LINE 1: SELECT '{1:1,}/1'::sparsevec;
               ^
SELECT '{1:0,2:1,3:0}/3'::sparsevec;
 sparsevec 
-----------
 {2:1}/3
(1 row)

SELECT '{2:1,1:1}/2'::sparsevec;
  sparsevec  
-------------
 {1:1,2:1}/2
(1 row)

SELECT '{1:1,1:1}/2'::sparsevec;
ERROR:  sparsevec indices must not contain duplicates
LINE 1: SELECT '{1:1,1:1}/2'::sparsevec;
               ^
SELECT '{1:1,2:1,1:1}/2'::sparsevec;
ERROR:  sparsevec indices must not contain duplicates
LINE 1: SELECT '{1:1,2:1,1:1}/2'::sparsevec;
               ^
SELECT '{}/5'::sparsevec;
 sparsevec 
-----------
 {}/5
(1 row)

SELECT '{}/-1'::sparsevec;
ERROR:  sparsevec must have at least 1 dimension
LINE 1: SELECT '{}/-1'::sparsevec;
               ^
SELECT '{}/1000000001'::sparsevec;
ERROR:  sparsevec cannot have more than 1000000000 dimensions
LINE 1: SELECT '{}/1000000001'::sparsevec;
               ^
SELECT '{}/2147483648'::sparsevec;
ERROR:  sparsevec cannot have more than 1000000000 dimensions
LINE 1: SELECT '{}/2147483648'::sparsevec;
               ^
SELECT '{}/-2147483649'::sparsevec;
ERROR:  sparsevec must have at least 1 dimension
LINE 1: SELECT '{}/-2147483649'::sparsevec;
               ^
SELECT '{}/9223372036854775808'::sparsevec;
ERROR:  sparsevec cannot have more than 1000000000 dimensions
LINE 1: SELECT '{}/9223372036854775808'::sparsevec;
               ^
SELECT '{}/-9223372036854775809'::sparsevec;
ERROR:  sparsevec must have at least 1 dimension
LINE 1: SELECT '{}/-9223372036854775809'::sparsevec;
               ^
SELECT '{2147483647:1}/1'::sparsevec;
ERROR:  sparsevec index out of bounds
LINE 1: SELECT '{2147483647:1}/1'::sparsevec;
               ^
SELECT '{2147483648:1}/1'::sparsevec;
ERROR:  sparsevec index out of bounds
LINE 1: SELECT '{2147483648:1}/1'::sparsevec;
               ^
SELECT '{-2147483648:1}/1'::sparsevec;
ERROR:  sparsevec index out of bounds
LINE 1: SELECT '{-2147483648:1}/1'::sparsevec;
               ^
SELECT '{-2147483649:1}/1'::sparsevec;
ERROR:  sparsevec index out of bounds
LINE 1: SELECT '{-2147483649:1}/1'::sparsevec;
               ^
SELECT '{0:1}/1'::sparsevec;
ERROR:  sparsevec index out of bounds
LINE 1: SELECT '{0:1}/1'::sparsevec;
               ^
SELECT '{2:1}/1'::sparsevec;
ERROR:  sparsevec index out of bounds
LINE 1: SELECT '{2:1}/1'::sparsevec;
               ^
SELECT '{}/3'::sparsevec(3);
 sparsevec 
-----------
 {}/3
(1 row)

SELECT '{}/3'::sparsevec(2);
ERROR:  expected 2 dimensions, not 3
SELECT '{}/3'::sparsevec(3, 2);
ERROR:  invalid type modifier
LINE 1: SELECT '{}/3'::sparsevec(3, 2);
                       ^
SELECT '{}/3'::sparsevec('a');
ERROR:  invalid input syntax for type integer: "a"
LINE 1: SELECT '{}/3'::sparsevec('a');
                       ^
SELECT '{}/3'::sparsevec(0);
ERROR:  dimensions for type sparsevec must be at least 1
LINE 1: SELECT '{}/3'::sparsevec(0);
                       ^
SELECT '{}/3'::sparsevec(1000000001);
ERROR:  dimensions for type sparsevec cannot exceed 1000000000
LINE 1: SELECT '{}/3'::sparsevec(1000000001);
                       ^
SELECT '{1:1,2:2,3:3}/3'::sparsevec < '{1:1,2:2,3:3}/3';
 ?column? 
----------
 f
(1 row)

SELECT '{1:1,2:2,3:3}/3'::sparsevec < '{1:1,2:2}/2';
 ?column? 
----------
 f
(1 row)

SELECT '{1:1,2:2,3:3}/3'::sparsevec <= '{1:1,2:2,3:3}/3';
 ?column? 
----------
 t
(1 row)

SELECT '{1:1,2:2,3:3}/3'::sparsevec <= '{1:1,2:2}/2';
 ?column? 
----------
 f
(1 row)

SELECT '{1:1,2:2,3:3}/3'::sparsevec = '{1:1,2:2,3:3}/3';
 ?column? 
----------
 t
(1 row)

SELECT '{1:1,2:2,3:3}/3'::sparsevec = '{1:1,2:2}/2';
 ?column? 
----------
 f
(1 row)

SELECT '{1:1,2:2,3:3}/3'::sparsevec != '{1:1,2:2,3:3}/3';
 ?column? 
----------
 f
(1 row)

SELECT '{1:1,2:2,3:3}/3'::sparsevec != '{1:1,2:2}/2';
 ?column? 
----------
 t
(1 row)

SELECT '{1:1,2:2,3:3}/3'::sparsevec >= '{1:1,2:2,3:3}/3';
 ?column? 
----------
 t
(1 row)

SELECT '{1:1,2:2,3:3}/3'::sparsevec >= '{1:1,2:2}/2';
 ?column? 
----------
 t
(1 row)

SELECT '{1:1,2:2,3:3}/3'::sparsevec > '{1:1,2:2,3:3}/3';
 ?column? 
----------
 f
(1 row)

SELECT '{1:1,2:2,3:3}/3'::sparsevec > '{1:1,2:2}/2';
 ?column? 
----------
 t
(1 row)

SELECT sparsevec_cmp('{1:1,2:2,3:3}/3', '{1:1,2:2,3:3}/3');
 sparsevec_cmp 
---------------
             0
(1 row)

SELECT sparsevec_cmp('{1:1,2:2,3:3}/3', '{}/3');
 sparsevec_cmp 
---------------
             1
(1 row)

SELECT sparsevec_cmp('{}/3', '{1:1,2:2,3:3}/3');
 sparsevec_cmp 
---------------
            -1
(1 row)

SELECT sparsevec_cmp('{1:1,2:2}/2', '{1:1,2:2,3:3}/3');
 sparsevec_cmp 
---------------
            -1
(1 row)

SELECT sparsevec_cmp('{1:1,2:2,3:3}/3', '{1:1,2:2}/2');
 sparsevec_cmp 
---------------
             1
(1 row)

SELECT sparsevec_cmp('{1:1,2:2}/2', '{1:2,2:3,3:4}/3');
 sparsevec_cmp 
---------------
            -1
(1 row)

SELECT sparsevec_cmp('{1:2,2:3}/2', '{1:1,2:2,3:3}/3');
 sparsevec_cmp 
---------------
             1
(1 row)

SELECT round(l2_norm('{1:1,2:1}/2'::sparsevec)::numeric, 5);
  round  
---------
 1.41421
(1 row)

SELECT l2_norm('{1:3,2:4}/2'::sparsevec);
 l2_norm 
---------
       5
(1 row)

SELECT l2_norm('{2:1}/2'::sparsevec);
 l2_norm 
---------
       1
(1 row)

SELECT l2_norm('{1:3e37,2:4e37}/2'::sparsevec)::real;
 l2_norm 
---------
   5e+37
(1 row)

SELECT l2_norm('{}/2'::sparsevec);
 l2_norm 
---------
       0
(1 row)

SELECT l2_norm('{1:2}/1'::sparsevec);
 l2_norm 
---------
       2
(1 row)

SELECT l2_distance('{}/2'::sparsevec, '{1:3,2:4}/2');
 l2_distance 
-------------
           5
(1 row)

SELECT l2_distance('{1:3}/2'::sparsevec, '{2:4}/2');
 l2_distance 
-------------
           5
(1 row)

SELECT l2_distance('{2:4}/2'::sparsevec, '{1:3}/2');
 l2_distance 
-------------
           5
(1 row)

SELECT l2_distance('{1:3,2:4}/2'::sparsevec, '{}/2');
 l2_distance 
-------------
           5
(1 row)

SELECT l2_distance('{}/2'::sparsevec, '{2:1}/2');
 l2_distance 
-------------
           1
(1 row)

SELECT '{}/2'::sparsevec <-> '{1:3,2:4}/2';
 ?column? 
----------
        5
(1 row)

SELECT inner_product('{1:1,2:2}/2'::sparsevec, '{1:2,2:4}/2');
 inner_product 
---------------
            10
(1 row)

SELECT inner_product('{1:1,2:2}/2'::sparsevec, '{1:3}/1');
ERROR:  different sparsevec dimensions 2 and 1
SELECT inner_product('{1:1,3:3}/4'::sparsevec, '{2:2,4:4}/4');
 inner_product 
---------------
             0
(1 row)

SELECT inner_product('{2:2,4:4}/4'::sparsevec, '{1:1,3:3}/4');
 inner_product 
---------------
             0
(1 row)

SELECT inner_product('{1:1,3:3,5:5}/5'::sparsevec, '{2:4,3:6,4:8}/5');
 inner_product 
---------------
            18
(1 row)

SELECT inner_product('{1:1}/2'::sparsevec, '{}/2');
 inner_product 
---------------
             0
(1 row)

SELECT inner_product('{}/2'::sparsevec, '{1:1}/2');
 inner_product 
---------------
             0
(1 row)

SELECT inner_product('{1:3e38}/1'::sparsevec, '{1:3e38}/1');
 inner_product 
---------------
      Infinity
(1 row)

SELECT inner_product('{1:1,3:3,5:5}/5'::sparsevec, '{2:4,3:6,4:8}/5');
 inner_product 
---------------
            18
(1 row)

SELECT '{1:1,2:2}/2'::sparsevec <#> '{1:3,2:4}/2';
 ?column? 
----------
      -11
(1 row)

SELECT cosine_distance('{1:1,2:2}/2'::sparsevec, '{1:2,2:4}/2');
 cosine_distance 
-----------------
               0
(1 row)

SELECT cosine_distance('{1:1,2:2}/2'::sparsevec, '{}/2');
 cosine_distance 
-----------------
             NaN
(1 row)

SELECT cosine_distance('{1:1,2:1}/2'::sparsevec, '{1:1,2:1}/2');
 cosine_distance 
-----------------
               0
(1 row)

SELECT cosine_distance('{1:1}/2'::sparsevec, '{2:2}/2');
 cosine_distance 
-----------------
               1
(1 row)

SELECT cosine_distance('{1:1,2:1}/2'::sparsevec, '{1:-1,2:-1}/2');
 cosine_distance 
-----------------
               2
(1 row)

SELECT cosine_distance('{1:2}/2'::sparsevec, '{2:2}/2');
 cosine_distance 
-----------------
               1
(1 row)

SELECT cosine_distance('{2:2}/2'::sparsevec, '{1:2}/2');
 cosine_distance 
-----------------
               1
(1 row)

SELECT cosine_distance('{1:1,2:2}/2'::sparsevec, '{1:3}/1');
ERROR:  different sparsevec dimensions 2 and 1
SELECT cosine_distance('{1:1,2:1}/2'::sparsevec, '{1:1.1,2:1.1}/2');
 cosine_distance 
-----------------
               0
(1 row)

SELECT cosine_distance('{1:1,2:1}/2'::sparsevec, '{1:-1.1,2:-1.1}/2');
 cosine_distance 
-----------------
               2
(1 row)

SELECT cosine_distance('{1:3e38}/1'::sparsevec, '{1:3e38}/1');
 cosine_distance 
-----------------
             NaN
(1 row)

SELECT cosine_distance('{}/1'::sparsevec, '{}/1');
 cosine_distance 
-----------------
             NaN
(1 row)

SELECT '{1:1,2:2}/2'::sparsevec <=> '{1:2,2:4}/2';
 ?column? 
----------
        0
(1 row)

SELECT l1_distance('{}/2'::sparsevec, '{1:3,2:4}/2');
 l1_distance 
-------------
           7
(1 row)

SELECT l1_distance('{}/2'::sparsevec, '{2:1}/2');
 l1_distance 
-------------
           1
(1 row)

SELECT l1_distance('{1:1,2:2}/2'::sparsevec, '{1:3}/1');
ERROR:  different sparsevec dimensions 2 and 1
SELECT l1_distance('{1:3e38}/1'::sparsevec, '{1:-3e38}/1');
 l1_distance 
-------------
    Infinity
(1 row)

SELECT l1_distance('{1:1,3:3,5:5,7:7}/8'::sparsevec, '{2:2,4:4,6:6,8:8}/8');
 l1_distance 
-------------
          36
(1 row)

SELECT l1_distance('{1:1,3:3,5:5,7:7,9:9}/9'::sparsevec, '{2:2,4:4,6:6,8:8}/9');
 l1_distance 
-------------
          45
(1 row)

SELECT '{}/2'::sparsevec <+> '{1:3,2:4}/2';
 ?column? 
----------
        7
(1 row)

SELECT l2_normalize('{1:3,2:4}/2'::sparsevec);
  l2_normalize   
-----------------
 {1:0.6,2:0.8}/2
(1 row)

SELECT l2_normalize('{1:3}/2'::sparsevec);
 l2_normalize 
--------------
 {1:1}/2
(1 row)

SELECT l2_normalize('{2:0.1}/2'::sparsevec);
 l2_normalize 
--------------
 {2:1}/2
(1 row)

SELECT l2_normalize('{}/2'::sparsevec);
 l2_normalize 
--------------
 {}/2
(1 row)

SELECT l2_normalize('{1:3e38}/1'::sparsevec);
 l2_normalize 
--------------
 {1:1}/1
(1 row)

SELECT l2_normalize('{1:3e38,2:1e-37}/2'::sparsevec);
 l2_normalize 
--------------
 {1:1}/2
(1 row)

SELECT l2_normalize('{2:3e37,4:3e-37,6:4e37,8:4e-37}/9'::sparsevec);
  l2_normalize   
-----------------
 {2:0.6,6:0.8}/9
(1 row)

