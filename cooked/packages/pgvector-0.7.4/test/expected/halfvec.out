SELECT '[1,2,3]'::halfvec;
 halfvec 
---------
 [1,2,3]
(1 row)

SELECT '[-1,-2,-3]'::halfvec;
  halfvec   
------------
 [-1,-2,-3]
(1 row)

SELECT '[1.,2.,3.]'::halfvec;
 halfvec 
---------
 [1,2,3]
(1 row)

SELECT ' [ 1,  2 ,    3  ] '::halfvec;
 halfvec 
---------
 [1,2,3]
(1 row)

SELECT '[1.23456]'::halfvec;
  halfvec   
------------
 [1.234375]
(1 row)

SELECT '[hello,1]'::halfvec;
ERROR:  invalid input syntax for type halfvec: "[hello,1]"
LINE 1: SELECT '[hello,1]'::halfvec;
               ^
SELECT '[NaN,1]'::halfvec;
ERROR:  NaN not allowed in halfvec
LINE 1: SELECT '[NaN,1]'::halfvec;
               ^
SELECT '[Infinity,1]'::halfvec;
ERROR:  infinite value not allowed in halfvec
LINE 1: SELECT '[Infinity,1]'::halfvec;
               ^
SELECT '[-Infinity,1]'::halfvec;
ERROR:  infinite value not allowed in halfvec
LINE 1: SELECT '[-Infinity,1]'::halfvec;
               ^
SELECT '[65519,-65519]'::halfvec;
    halfvec     
----------------
 [65504,-65504]
(1 row)

SELECT '[65520,-65520]'::halfvec;
ERROR:  "65520" is out of range for type halfvec
LINE 1: SELECT '[65520,-65520]'::halfvec;
               ^
SELECT '[1e-8,-1e-8]'::halfvec;
 halfvec 
---------
 [0,-0]
(1 row)

SELECT '[4e38,1]'::halfvec;
ERROR:  "4e38" is out of range for type halfvec
LINE 1: SELECT '[4e38,1]'::halfvec;
               ^
SELECT '[1e-46,1]'::halfvec;
 halfvec 
---------
 [0,1]
(1 row)

SELECT '[1,2,3'::halfvec;
ERROR:  invalid input syntax for type halfvec: "[1,2,3"
LINE 1: SELECT '[1,2,3'::halfvec;
               ^
SELECT '[1,2,3]9'::halfvec;
ERROR:  invalid input syntax for type halfvec: "[1,2,3]9"
LINE 1: SELECT '[1,2,3]9'::halfvec;
               ^
DETAIL:  Junk after closing right brace.
SELECT '1,2,3'::halfvec;
ERROR:  invalid input syntax for type halfvec: "1,2,3"
LINE 1: SELECT '1,2,3'::halfvec;
               ^
DETAIL:  Vector contents must start with "[".
SELECT ''::halfvec;
ERROR:  invalid input syntax for type halfvec: ""
LINE 1: SELECT ''::halfvec;
               ^
DETAIL:  Vector contents must start with "[".
SELECT '['::halfvec;
ERROR:  invalid input syntax for type halfvec: "["
LINE 1: SELECT '['::halfvec;
               ^
SELECT '[ '::halfvec;
ERROR:  invalid input syntax for type halfvec: "[ "
LINE 1: SELECT '[ '::halfvec;
               ^
SELECT '[,'::halfvec;
ERROR:  invalid input syntax for type halfvec: "[,"
LINE 1: SELECT '[,'::halfvec;
               ^
SELECT '[]'::halfvec;
ERROR:  halfvec must have at least 1 dimension
LINE 1: SELECT '[]'::halfvec;
               ^
SELECT '[ ]'::halfvec;
ERROR:  halfvec must have at least 1 dimension
LINE 1: SELECT '[ ]'::halfvec;
               ^
SELECT '[,]'::halfvec;
ERROR:  invalid input syntax for type halfvec: "[,]"
LINE 1: SELECT '[,]'::halfvec;
               ^
SELECT '[1,]'::halfvec;
ERROR:  invalid input syntax for type halfvec: "[1,]"
LINE 1: SELECT '[1,]'::halfvec;
               ^
SELECT '[1a]'::halfvec;
ERROR:  invalid input syntax for type halfvec: "[1a]"
LINE 1: SELECT '[1a]'::halfvec;
               ^
SELECT '[1,,3]'::halfvec;
ERROR:  invalid input syntax for type halfvec: "[1,,3]"
LINE 1: SELECT '[1,,3]'::halfvec;
               ^
SELECT '[1, ,3]'::halfvec;
ERROR:  invalid input syntax for type halfvec: "[1, ,3]"
LINE 1: SELECT '[1, ,3]'::halfvec;
               ^
SELECT '[1,2,3]'::halfvec(3);
 halfvec 
---------
 [1,2,3]
(1 row)

SELECT '[1,2,3]'::halfvec(2);
ERROR:  expected 2 dimensions, not 3
SELECT '[1,2,3]'::halfvec(3, 2);
ERROR:  invalid type modifier
LINE 1: SELECT '[1,2,3]'::halfvec(3, 2);
                          ^
SELECT '[1,2,3]'::halfvec('a');
ERROR:  invalid input syntax for type integer: "a"
LINE 1: SELECT '[1,2,3]'::halfvec('a');
                          ^
SELECT '[1,2,3]'::halfvec(0);
ERROR:  dimensions for type halfvec must be at least 1
LINE 1: SELECT '[1,2,3]'::halfvec(0);
                          ^
SELECT '[1,2,3]'::halfvec(16001);
ERROR:  dimensions for type halfvec cannot exceed 16000
LINE 1: SELECT '[1,2,3]'::halfvec(16001);
                          ^
SELECT unnest('{"[1,2,3]", "[4,5,6]"}'::halfvec[]);
 unnest  
---------
 [1,2,3]
 [4,5,6]
(2 rows)

SELECT '{"[1,2,3]"}'::halfvec(2)[];
ERROR:  expected 2 dimensions, not 3
SELECT '[1,2,3]'::halfvec + '[4,5,6]';
 ?column? 
----------
 [5,7,9]
(1 row)

SELECT '[65519]'::halfvec + '[65519]';
ERROR:  value out of range: overflow
SELECT '[1,2]'::halfvec + '[3]';
ERROR:  different halfvec dimensions 2 and 1
SELECT '[1,2,3]'::halfvec - '[4,5,6]';
  ?column?  
------------
 [-3,-3,-3]
(1 row)

SELECT '[-65519]'::halfvec - '[65519]';
ERROR:  value out of range: overflow
SELECT '[1,2]'::halfvec - '[3]';
ERROR:  different halfvec dimensions 2 and 1
SELECT '[1,2,3]'::halfvec * '[4,5,6]';
 ?column?  
-----------
 [4,10,18]
(1 row)

SELECT '[65519]'::halfvec * '[65519]';
ERROR:  value out of range: overflow
SELECT '[1e-7]'::halfvec * '[1e-7]';
ERROR:  value out of range: underflow
SELECT '[1,2]'::halfvec * '[3]';
ERROR:  different halfvec dimensions 2 and 1
SELECT '[1,2,3]'::halfvec || '[4,5]';
  ?column?   
-------------
 [1,2,3,4,5]
(1 row)

SELECT array_fill(0, ARRAY[16000])::halfvec || '[1]';
ERROR:  halfvec cannot have more than 16000 dimensions
SELECT '[1,2,3]'::halfvec < '[1,2,3]';
 ?column? 
----------
 f
(1 row)

SELECT '[1,2,3]'::halfvec < '[1,2]';
 ?column? 
----------
 f
(1 row)

SELECT '[1,2,3]'::halfvec <= '[1,2,3]';
 ?column? 
----------
 t
(1 row)

SELECT '[1,2,3]'::halfvec <= '[1,2]';
 ?column? 
----------
 f
(1 row)

SELECT '[1,2,3]'::halfvec = '[1,2,3]';
 ?column? 
----------
 t
(1 row)

SELECT '[1,2,3]'::halfvec = '[1,2]';
 ?column? 
----------
 f
(1 row)

SELECT '[1,2,3]'::halfvec != '[1,2,3]';
 ?column? 
----------
 f
(1 row)

SELECT '[1,2,3]'::halfvec != '[1,2]';
 ?column? 
----------
 t
(1 row)

SELECT '[1,2,3]'::halfvec >= '[1,2,3]';
 ?column? 
----------
 t
(1 row)

SELECT '[1,2,3]'::halfvec >= '[1,2]';
 ?column? 
----------
 t
(1 row)

SELECT '[1,2,3]'::halfvec > '[1,2,3]';
 ?column? 
----------
 f
(1 row)

SELECT '[1,2,3]'::halfvec > '[1,2]';
 ?column? 
----------
 t
(1 row)

SELECT halfvec_cmp('[1,2,3]', '[1,2,3]');
 halfvec_cmp 
-------------
           0
(1 row)

SELECT halfvec_cmp('[1,2,3]', '[0,0,0]');
 halfvec_cmp 
-------------
           1
(1 row)

SELECT halfvec_cmp('[0,0,0]', '[1,2,3]');
 halfvec_cmp 
-------------
          -1
(1 row)

SELECT halfvec_cmp('[1,2]', '[1,2,3]');
 halfvec_cmp 
-------------
          -1
(1 row)

SELECT halfvec_cmp('[1,2,3]', '[1,2]');
 halfvec_cmp 
-------------
           1
(1 row)

SELECT halfvec_cmp('[1,2]', '[2,3,4]');
 halfvec_cmp 
-------------
          -1
(1 row)

SELECT halfvec_cmp('[2,3]', '[1,2,3]');
 halfvec_cmp 
-------------
           1
(1 row)

SELECT vector_dims('[1,2,3]'::halfvec);
 vector_dims 
-------------
           3
(1 row)

SELECT round(l2_norm('[1,1]'::halfvec)::numeric, 5);
  round  
---------
 1.41421
(1 row)

SELECT l2_norm('[3,4]'::halfvec);
 l2_norm 
---------
       5
(1 row)

SELECT l2_norm('[0,1]'::halfvec);
 l2_norm 
---------
       1
(1 row)

SELECT l2_norm('[0,0]'::halfvec);
 l2_norm 
---------
       0
(1 row)

SELECT l2_norm('[2]'::halfvec);
 l2_norm 
---------
       2
(1 row)

SELECT l2_distance('[0,0]'::halfvec, '[3,4]');
 l2_distance 
-------------
           5
(1 row)

SELECT l2_distance('[0,0]'::halfvec, '[0,1]');
 l2_distance 
-------------
           1
(1 row)

SELECT l2_distance('[1,2]'::halfvec, '[3]');
ERROR:  different halfvec dimensions 2 and 1
SELECT l2_distance('[1,1,1,1,1,1,1,1,1]'::halfvec, '[1,1,1,1,1,1,1,4,5]');
 l2_distance 
-------------
           5
(1 row)

SELECT '[0,0]'::halfvec <-> '[3,4]';
 ?column? 
----------
        5
(1 row)

SELECT inner_product('[1,2]'::halfvec, '[3,4]');
 inner_product 
---------------
            11
(1 row)

SELECT inner_product('[1,2]'::halfvec, '[3]');
ERROR:  different halfvec dimensions 2 and 1
SELECT inner_product('[65504]'::halfvec, '[65504]');
 inner_product 
---------------
    4290774016
(1 row)

SELECT inner_product('[1,1,1,1,1,1,1,1,1]'::halfvec, '[1,2,3,4,5,6,7,8,9]');
 inner_product 
---------------
            45
(1 row)

SELECT '[1,2]'::halfvec <#> '[3,4]';
 ?column? 
----------
      -11
(1 row)

SELECT cosine_distance('[1,2]'::halfvec, '[2,4]');
 cosine_distance 
-----------------
               0
(1 row)

SELECT cosine_distance('[1,2]'::halfvec, '[0,0]');
 cosine_distance 
-----------------
             NaN
(1 row)

SELECT cosine_distance('[1,1]'::halfvec, '[1,1]');
 cosine_distance 
-----------------
               0
(1 row)

SELECT cosine_distance('[1,0]'::halfvec, '[0,2]');
 cosine_distance 
-----------------
               1
(1 row)

SELECT cosine_distance('[1,1]'::halfvec, '[-1,-1]');
 cosine_distance 
-----------------
               2
(1 row)

SELECT cosine_distance('[1,2]'::halfvec, '[3]');
ERROR:  different halfvec dimensions 2 and 1
SELECT cosine_distance('[1,1]'::halfvec, '[1.1,1.1]');
 cosine_distance 
-----------------
               0
(1 row)

SELECT cosine_distance('[1,1]'::halfvec, '[-1.1,-1.1]');
 cosine_distance 
-----------------
               2
(1 row)

SELECT cosine_distance('[1,2,3,4,5,6,7,8,9]'::halfvec, '[1,2,3,4,5,6,7,8,9]');
 cosine_distance 
-----------------
               0
(1 row)

SELECT cosine_distance('[1,2,3,4,5,6,7,8,9]'::halfvec, '[-1,-2,-3,-4,-5,-6,-7,-8,-9]');
 cosine_distance 
-----------------
               2
(1 row)

SELECT '[1,2]'::halfvec <=> '[2,4]';
 ?column? 
----------
        0
(1 row)

SELECT l1_distance('[0,0]'::halfvec, '[3,4]');
 l1_distance 
-------------
           7
(1 row)

SELECT l1_distance('[0,0]'::halfvec, '[0,1]');
 l1_distance 
-------------
           1
(1 row)

SELECT l1_distance('[1,2]'::halfvec, '[3]');
ERROR:  different halfvec dimensions 2 and 1
SELECT l1_distance('[1,2,3,4,5,6,7,8,9]'::halfvec, '[1,2,3,4,5,6,7,8,9]');
 l1_distance 
-------------
           0
(1 row)

SELECT l1_distance('[1,2,3,4,5,6,7,8,9]'::halfvec, '[0,3,2,5,4,7,6,9,8]');
 l1_distance 
-------------
           9
(1 row)

SELECT '[0,0]'::halfvec <+> '[3,4]';
 ?column? 
----------
        7
(1 row)

SELECT l2_normalize('[3,4]'::halfvec);
      l2_normalize      
------------------------
 [0.60009766,0.7998047]
(1 row)

SELECT l2_normalize('[3,0]'::halfvec);
 l2_normalize 
--------------
 [1,0]
(1 row)

SELECT l2_normalize('[0,0.1]'::halfvec);
 l2_normalize 
--------------
 [0,1]
(1 row)

SELECT l2_normalize('[0,0]'::halfvec);
 l2_normalize 
--------------
 [0,0]
(1 row)

SELECT l2_normalize('[65504]'::halfvec);
 l2_normalize 
--------------
 [1]
(1 row)

SELECT binary_quantize('[1,0,-1]'::halfvec);
 binary_quantize 
-----------------
 100
(1 row)

SELECT binary_quantize('[0,0.1,-0.2,-0.3,0.4,0.5,0.6,-0.7,0.8,-0.9,1]'::halfvec);
 binary_quantize 
-----------------
 01001110101
(1 row)

SELECT subvector('[1,2,3,4,5]'::halfvec, 1, 3);
 subvector 
-----------
 [1,2,3]
(1 row)

SELECT subvector('[1,2,3,4,5]'::halfvec, 3, 2);
 subvector 
-----------
 [3,4]
(1 row)

SELECT subvector('[1,2,3,4,5]'::halfvec, -1, 3);
 subvector 
-----------
 [1]
(1 row)

SELECT subvector('[1,2,3,4,5]'::halfvec, 3, 9);
 subvector 
-----------
 [3,4,5]
(1 row)

SELECT subvector('[1,2,3,4,5]'::halfvec, 1, 0);
ERROR:  halfvec must have at least 1 dimension
SELECT subvector('[1,2,3,4,5]'::halfvec, 3, -1);
ERROR:  halfvec must have at least 1 dimension
SELECT subvector('[1,2,3,4,5]'::halfvec, -1, 2);
ERROR:  halfvec must have at least 1 dimension
SELECT subvector('[1,2,3,4,5]'::halfvec, 2147483647, 10);
ERROR:  halfvec must have at least 1 dimension
SELECT subvector('[1,2,3,4,5]'::halfvec, 3, 2147483647);
 subvector 
-----------
 [3,4,5]
(1 row)

SELECT subvector('[1,2,3,4,5]'::halfvec, -2147483644, 2147483647);
 subvector 
-----------
 [1,2]
(1 row)

SELECT avg(v) FROM unnest(ARRAY['[1,2,3]'::halfvec, '[3,5,7]']) v;
    avg    
-----------
 [2,3.5,5]
(1 row)

SELECT avg(v) FROM unnest(ARRAY['[1,2,3]'::halfvec, '[3,5,7]', NULL]) v;
    avg    
-----------
 [2,3.5,5]
(1 row)

SELECT avg(v) FROM unnest(ARRAY[]::halfvec[]) v;
 avg 
-----
 
(1 row)

SELECT avg(v) FROM unnest(ARRAY['[1,2]'::halfvec, '[3]']) v;
ERROR:  expected 2 dimensions, not 1
SELECT avg(v) FROM unnest(ARRAY['[65504]'::halfvec, '[65504]']) v;
   avg   
---------
 [65504]
(1 row)

SELECT halfvec_avg(array_agg(n)) FROM generate_series(1, 16002) n;
ERROR:  halfvec cannot have more than 16000 dimensions
SELECT sum(v) FROM unnest(ARRAY['[1,2,3]'::halfvec, '[3,5,7]']) v;
   sum    
----------
 [4,7,10]
(1 row)

SELECT sum(v) FROM unnest(ARRAY['[1,2,3]'::halfvec, '[3,5,7]', NULL]) v;
   sum    
----------
 [4,7,10]
(1 row)

SELECT sum(v) FROM unnest(ARRAY[]::halfvec[]) v;
 sum 
-----
 
(1 row)

SELECT sum(v) FROM unnest(ARRAY['[1,2]'::halfvec, '[3]']) v;
ERROR:  different halfvec dimensions 2 and 1
SELECT sum(v) FROM unnest(ARRAY['[65504]'::halfvec, '[65504]']) v;
ERROR:  value out of range: overflow
