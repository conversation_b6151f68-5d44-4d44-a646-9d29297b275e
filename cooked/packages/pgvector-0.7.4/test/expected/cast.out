SELECT ARRAY[1,2,3]::vector;
  array  
---------
 [1,2,3]
(1 row)

SELECT ARRAY[1.0,2.0,3.0]::vector;
  array  
---------
 [1,2,3]
(1 row)

SELECT ARRAY[1,2,3]::float4[]::vector;
  array  
---------
 [1,2,3]
(1 row)

SELECT ARRAY[1,2,3]::float8[]::vector;
  array  
---------
 [1,2,3]
(1 row)

SELECT ARRAY[1,2,3]::numeric[]::vector;
  array  
---------
 [1,2,3]
(1 row)

SELECT '[1,2,3]'::vector::real[];
 float4  
---------
 {1,2,3}
(1 row)

SELECT '{1,2,3}'::real[]::vector;
 vector  
---------
 [1,2,3]
(1 row)

SELECT '{1,2,3}'::real[]::vector(3);
 vector  
---------
 [1,2,3]
(1 row)

SELECT '{1,2,3}'::real[]::vector(2);
ERROR:  expected 2 dimensions, not 3
SELECT '{NULL}'::real[]::vector;
ERROR:  array must not contain nulls
SELECT '{NaN}'::real[]::vector;
ERROR:  NaN not allowed in vector
SELECT '{Infinity}'::real[]::vector;
ERROR:  infinite value not allowed in vector
SELECT '{-Infinity}'::real[]::vector;
ERROR:  infinite value not allowed in vector
SELECT '{}'::real[]::vector;
ERROR:  vector must have at least 1 dimension
SELECT '{{1}}'::real[]::vector;
ERROR:  array must be 1-D
SELECT '{1,2,3}'::double precision[]::vector;
 vector  
---------
 [1,2,3]
(1 row)

SELECT '{1,2,3}'::double precision[]::vector(3);
 vector  
---------
 [1,2,3]
(1 row)

SELECT '{1,2,3}'::double precision[]::vector(2);
ERROR:  expected 2 dimensions, not 3
SELECT '{4e38,-4e38}'::double precision[]::vector;
ERROR:  infinite value not allowed in vector
SELECT '{1e-46,-1e-46}'::double precision[]::vector;
 vector 
--------
 [0,-0]
(1 row)

SELECT '[1,2,3]'::vector::halfvec;
 halfvec 
---------
 [1,2,3]
(1 row)

SELECT '[1,2,3]'::vector::halfvec(3);
 halfvec 
---------
 [1,2,3]
(1 row)

SELECT '[1,2,3]'::vector::halfvec(2);
ERROR:  expected 2 dimensions, not 3
SELECT '[65520]'::vector::halfvec;
ERROR:  "65520" is out of range for type halfvec
SELECT '[1e-8]'::vector::halfvec;
 halfvec 
---------
 [0]
(1 row)

SELECT '[1,2,3]'::halfvec::vector;
 vector  
---------
 [1,2,3]
(1 row)

SELECT '[1,2,3]'::halfvec::vector(3);
 vector  
---------
 [1,2,3]
(1 row)

SELECT '[1,2,3]'::halfvec::vector(2);
ERROR:  expected 2 dimensions, not 3
SELECT '{1,2,3}'::real[]::halfvec;
 halfvec 
---------
 [1,2,3]
(1 row)

SELECT '{1,2,3}'::real[]::halfvec(3);
 halfvec 
---------
 [1,2,3]
(1 row)

SELECT '{1,2,3}'::real[]::halfvec(2);
ERROR:  expected 2 dimensions, not 3
SELECT '{65520,-65520}'::real[]::halfvec;
ERROR:  "65520" is out of range for type halfvec
SELECT '{1e-8,-1e-8}'::real[]::halfvec;
 halfvec 
---------
 [0,-0]
(1 row)

SELECT '[0,1.5,0,3.5,0]'::vector::sparsevec;
    sparsevec    
-----------------
 {2:1.5,4:3.5}/5
(1 row)

SELECT '[0,1.5,0,3.5,0]'::vector::sparsevec(5);
    sparsevec    
-----------------
 {2:1.5,4:3.5}/5
(1 row)

SELECT '[0,1.5,0,3.5,0]'::vector::sparsevec(4);
ERROR:  expected 4 dimensions, not 5
SELECT '{2:1.5,4:3.5}/5'::sparsevec::vector;
     vector      
-----------------
 [0,1.5,0,3.5,0]
(1 row)

SELECT '{2:1.5,4:3.5}/5'::sparsevec::vector(5);
     vector      
-----------------
 [0,1.5,0,3.5,0]
(1 row)

SELECT '{2:1.5,4:3.5}/5'::sparsevec::vector(4);
ERROR:  expected 4 dimensions, not 5
SELECT '{}/16001'::sparsevec::vector;
ERROR:  vector cannot have more than 16000 dimensions
SELECT '[0,1.5,0,3.5,0]'::halfvec::sparsevec;
    sparsevec    
-----------------
 {2:1.5,4:3.5}/5
(1 row)

SELECT '[0,1.5,0,3.5,0]'::halfvec::sparsevec(5);
    sparsevec    
-----------------
 {2:1.5,4:3.5}/5
(1 row)

SELECT '[0,1.5,0,3.5,0]'::halfvec::sparsevec(4);
ERROR:  expected 4 dimensions, not 5
SELECT '{2:1.5,4:3.5}/5'::sparsevec::halfvec;
     halfvec     
-----------------
 [0,1.5,0,3.5,0]
(1 row)

SELECT '{2:1.5,4:3.5}/5'::sparsevec::halfvec(5);
     halfvec     
-----------------
 [0,1.5,0,3.5,0]
(1 row)

SELECT '{2:1.5,4:3.5}/5'::sparsevec::halfvec(4);
ERROR:  expected 4 dimensions, not 5
SELECT '{}/16001'::sparsevec::halfvec;
ERROR:  halfvec cannot have more than 16000 dimensions
SELECT '{1:65520}/1'::sparsevec::halfvec;
ERROR:  "65520" is out of range for type halfvec
SELECT '{1:1e-8}/1'::sparsevec::halfvec;
 halfvec 
---------
 [0]
(1 row)

SELECT array_agg(n)::vector FROM generate_series(1, 16001) n;
ERROR:  vector cannot have more than 16000 dimensions
SELECT array_to_vector(array_agg(n), 16001, false) FROM generate_series(1, 16001) n;
ERROR:  vector cannot have more than 16000 dimensions
-- ensure no error
SELECT ARRAY[1,2,3] = ARRAY[1,2,3];
 ?column? 
----------
 t
(1 row)

