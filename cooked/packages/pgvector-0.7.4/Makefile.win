EXTENSION = vector
EXTVERSION = 0.7.4

DATA_built = sql\$(EXTENSION)--$(EXTVERSION).sql
OBJS = src\bitutils.obj src\bitvec.obj src\halfutils.obj src\halfvec.obj src\hnsw.obj src\hnswbuild.obj src\hnswinsert.obj src\hnswscan.obj src\hnswutils.obj src\hnswvacuum.obj src\ivfbuild.obj src\ivfflat.obj src\ivfinsert.obj src\ivfkmeans.obj src\ivfscan.obj src\ivfutils.obj src\ivfvacuum.obj src\sparsevec.obj src\vector.obj
HEADERS = src\halfvec.h src\sparsevec.h src\vector.h

REGRESS = bit btree cast copy halfvec hnsw_bit hnsw_halfvec hnsw_sparsevec hnsw_vector ivfflat_bit ivfflat_halfvec ivfflat_vector sparsevec vector_type
REGRESS_OPTS = --inputdir=test --load-extension=$(EXTENSION)

# For /arch flags
# https://learn.microsoft.com/en-us/cpp/build/reference/arch-minimum-cpu-architecture
OPTFLAGS =

# For auto-vectorization:
# - MSVC (needs /O2 /fp:fast) - https://learn.microsoft.com/en-us/cpp/parallel/auto-parallelization-and-auto-vectorization?#auto-vectorizer
PG_CFLAGS = $(PG_CFLAGS) $(OPTFLAGS) /O2 /fp:fast

# Debug MSVC auto-vectorization
# https://learn.microsoft.com/en-us/cpp/error-messages/tool-errors/vectorizer-and-parallelizer-messages
# PG_CFLAGS = $(PG_CFLAGS) /Qvec-report:2

# TODO use pg_config
!ifndef PGROOT
!error PGROOT is not set
!endif
BINDIR = $(PGROOT)\bin
INCLUDEDIR = $(PGROOT)\include
INCLUDEDIR_SERVER = $(PGROOT)\include\server
LIBDIR = $(PGROOT)\lib
PKGLIBDIR = $(PGROOT)\lib
SHAREDIR = $(PGROOT)\share

CFLAGS = /nologo /I"$(INCLUDEDIR_SERVER)\port\win32_msvc" /I"$(INCLUDEDIR_SERVER)\port\win32" /I"$(INCLUDEDIR_SERVER)" /I"$(INCLUDEDIR)"

CFLAGS = $(CFLAGS) $(PG_CFLAGS)

SHLIB = $(EXTENSION).dll

LIBS = "$(LIBDIR)\postgres.lib"

all: $(SHLIB) $(DATA_built)

.c.obj:
	$(CC) $(CFLAGS) /c $< /Fo$@

$(SHLIB): $(OBJS)
	$(CC) $(CFLAGS) $(OBJS) $(LIBS) /link /DLL /OUT:$(SHLIB)

sql\$(EXTENSION)--$(EXTVERSION).sql: sql\$(EXTENSION).sql
	copy sql\$(EXTENSION).sql $@

install: all
	copy $(SHLIB) "$(PKGLIBDIR)"
	copy $(EXTENSION).control "$(SHAREDIR)\extension"
	copy sql\$(EXTENSION)--*.sql "$(SHAREDIR)\extension"
	mkdir "$(INCLUDEDIR_SERVER)\extension\$(EXTENSION)"
	for %f in ($(HEADERS)) do copy %f "$(INCLUDEDIR_SERVER)\extension\$(EXTENSION)"

installcheck:
	"$(BINDIR)\pg_regress" --bindir="$(BINDIR)" $(REGRESS_OPTS) $(REGRESS)

uninstall:
	del /f "$(PKGLIBDIR)\$(SHLIB)"
	del /f "$(SHAREDIR)\extension\$(EXTENSION).control"
	del /f "$(SHAREDIR)\extension\$(EXTENSION)--*.sql"
	del /f "$(INCLUDEDIR_SERVER)\extension\$(EXTENSION)\*.h"
	rmdir "$(INCLUDEDIR_SERVER)\extension\$(EXTENSION)"

clean:
	del /f $(SHLIB) $(EXTENSION).lib $(EXTENSION).exp
	del /f $(DATA_built)
	del /f $(OBJS)
	del /f /s /q results regression.diffs regression.out tmp_check tmp_check_iso log output_iso
