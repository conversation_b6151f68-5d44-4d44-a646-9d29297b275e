name: build
on: [push, pull_request]
jobs:
  ubuntu:
    runs-on: ${{ matrix.os }}
    if: ${{ !startsWith(github.ref_name, 'mac') && !startsWith(github.ref_name, 'windows') }}
    strategy:
      fail-fast: false
      matrix:
        include:
          - postgres: 17
            os: ubuntu-24.04
          - postgres: 16
            os: ubuntu-24.04
          - postgres: 15
            os: ubuntu-22.04
          - postgres: 14
            os: ubuntu-22.04
          - postgres: 13
            os: ubuntu-20.04
          - postgres: 12
            os: ubuntu-20.04
    steps:
      - uses: actions/checkout@v4
      - uses: ankane/setup-postgres@v1
        with:
          postgres-version: ${{ matrix.postgres }}
          dev-files: true
      - run: make
        env:
          PG_CFLAGS: -DUSE_ASSERT_CHECKING -Wall -Wextra -Werror -Wno-unused-parameter -Wno-sign-compare
      - run: |
          export PG_CONFIG=`which pg_config`
          sudo --preserve-env=PG_CONFIG make install
      - run: make installcheck
      - if: ${{ failure() }}
        run: cat regression.diffs
      - run: |
          sudo apt-get update
          sudo apt-get install libipc-run-perl
      - run: make prove_installcheck
  mac:
    runs-on: ${{ matrix.os }}
    if: ${{ !startsWith(github.ref_name, 'windows') }}
    strategy:
      fail-fast: false
      matrix:
        include:
          - postgres: 16
            os: macos-14
          - postgres: 14
            os: macos-12
    steps:
      - uses: actions/checkout@v4
      - uses: ankane/setup-postgres@v1
        with:
          postgres-version: ${{ matrix.postgres }}
      - run: make
        env:
          PG_CFLAGS: -DUSE_ASSERT_CHECKING -Wall -Wextra -Werror -Wno-unused-parameter
      - run: make install
      - run: make installcheck
      - if: ${{ failure() }}
        run: cat regression.diffs
      # Homebrew Postgres does not enable TAP tests, so need to download
      - run: |
          brew install cpanm
          cpanm --notest IPC::Run
          wget -q https://github.com/postgres/postgres/archive/refs/tags/$TAG.tar.gz
          tar xf $TAG.tar.gz
          mv postgres-$TAG postgres
        env:
          TAG: ${{ matrix.postgres == 16 && 'REL_16_2' || 'REL_14_11' }}
      - run: make prove_installcheck PROVE_FLAGS="-I ./postgres/src/test/perl -I ./test/perl"
        env:
          PERL5LIB: /Users/<USER>/perl5/lib/perl5
      - run: make clean && $(brew --prefix llvm@15)/bin/scan-build --status-bugs make
        env:
          PG_CFLAGS: -DUSE_ASSERT_CHECKING
  windows:
    runs-on: windows-latest
    if: ${{ !startsWith(github.ref_name, 'mac') }}
    steps:
      - uses: actions/checkout@v4
      - uses: ankane/setup-postgres@v1
        with:
          postgres-version: 14
      - run: |
          call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat" && ^
          nmake /NOLOGO /F Makefile.win && ^
          nmake /NOLOGO /F Makefile.win install && ^
          nmake /NOLOGO /F Makefile.win installcheck && ^
          nmake /NOLOGO /F Makefile.win clean && ^
          nmake /NOLOGO /F Makefile.win uninstall
        shell: cmd
      - if: ${{ failure() }}
        run: cat regression.diffs
  i386:
    if: ${{ !startsWith(github.ref_name, 'mac') && !startsWith(github.ref_name, 'windows') }}
    runs-on: ubuntu-latest
    container:
      image: debian:12
      options: --platform linux/386
    steps:
      - run: apt-get update && apt-get install -y build-essential git libipc-run-perl postgresql-15 postgresql-server-dev-15 sudo
      - run: service postgresql start
      - run: |
          git clone https://github.com/${{ github.repository }}.git pgvector
          cd pgvector
          git fetch origin ${{ github.ref }}
          git reset --hard FETCH_HEAD
          make
          make install
          chown -R postgres .
          sudo -u postgres make installcheck
          sudo -u postgres make prove_installcheck
        env:
          PG_CFLAGS: -DUSE_ASSERT_CHECKING -Wall -Wextra -Werror -Wno-unused-parameter -Wno-sign-compare
      - if: ${{ failure() }}
        run: cat pgvector/regression.diffs
  valgrind:
    if: ${{ !startsWith(github.ref_name, 'mac') && !startsWith(github.ref_name, 'windows') }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ankane/setup-postgres-valgrind@v1
        with:
          postgres-version: 16
          check-ub: yes
      - run: make OPTFLAGS=""
      - run: sudo --preserve-env=PG_CONFIG make install
      - run: make installcheck
