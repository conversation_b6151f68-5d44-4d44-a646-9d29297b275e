{"name": "vector", "abstract": "Open-source vector similarity search for Postgres", "description": "Supports L2 distance, inner product, and cosine distance", "version": "0.7.4", "maintainer": ["<PERSON> <<EMAIL>>"], "license": {"PostgreSQL": "http://www.postgresql.org/about/licence"}, "prereqs": {"runtime": {"requires": {"PostgreSQL": "12.0.0"}}}, "provides": {"vector": {"file": "sql/vector.sql", "docfile": "README.md", "version": "0.7.4", "abstract": "Open-source vector similarity search for Postgres"}}, "resources": {"homepage": "https://github.com/pgvector/pgvector", "bugtracker": {"web": "https://github.com/pgvector/pgvector/issues"}, "repository": {"url": "https://github.com/pgvector/pgvector.git", "web": "https://github.com/pgvector/pgvector", "type": "git"}}, "generated_by": "<PERSON>", "meta-spec": {"version": "1.0.0", "url": "http://pgxn.org/meta/spec.txt"}, "tags": ["vectors", "datatype", "nearest neighbor search", "approximate nearest neighbors"]}