#
# NOTE:
#   This file is NOT loaded by the PostgreSQL database.  It just serves as
#   a template for timezones you could need.  See the `Date/Time Support'
#   appendix in the PostgreSQL documentation for more information.
#
# src/timezone/tznames/Australia.txt
#

ACSST   37800 D  # Australian Central Summer Standard Time (not in IANA database)
ACDT    37800 D  # Australian Central Daylight Time
                 #     (Australia/Adelaide)
                 #     (Australia/Broken_Hill)
                 #     (Australia/Darwin)
ACST    34200    # Australian Central Standard Time
                 #     (Australia/Adelaide)
                 #     (Australia/Broken_Hill)
                 #     (Australia/Darwin)
ACWST   31500    # Australian Central Western Standard Time (obsolete)
AESST   39600 D  # Australian Eastern Summer Standard Time (not in IANA database)
AEDT    39600 D  # Australian Eastern Daylight Time
                 #     (Australia/Brisbane)
                 #     (Australia/Currie)
                 #     (Australia/Hobart)
                 #     (Australia/Lindeman)
                 #     (Australia/Melbourne)
                 #     (Australia/Sydney)
AEST    36000    # Australian Eastern Standard Time
                 #     (Australia/Brisbane)
                 #     (Australia/Currie)
                 #     (Australia/Hobart)
                 #     (Australia/Lindeman)
                 #     (Australia/Melbourne)
                 #     (Australia/Sydney)
AWSST   32400 D  # Australia Western Summer Standard Time (not in IANA database)
AWST    28800    # Australian Western Standard Time
                 #     (Australia/Perth)
CADT    37800 D  # Central Australia Daylight-Saving Time (not in IANA database)
CAST    34200    # Central Australia Standard Time (not in IANA database)
# CONFLICT! CST is not unique
# Other timezones:
#  - CST: Central Standard Time (America)
#  - CST: China Standard Time (Asia)
#  - CST: Cuba Central Standard Time (America)
CST     34200    # Central Standard Time (not in IANA database)
CWST    31500    # Central Western Standard Time (not in IANA database)
# CONFLICT! EAST is not unique
# Other timezones:
#  - EAST: Easter Island Time (Chile) (Pacific)
EAST    36000    # East Australian Standard Time (not in IANA database)
# CONFLICT! EST is not unique
# Other timezones:
#  - EST: Eastern Standard Time (America)
EST     36000    # Eastern Standard Time (not in IANA database)
LHDT    Australia/Lord_Howe  # Lord Howe Daylight Time (obsolete)
LHST    37800    # Lord Howe Standard Time (obsolete)
LIGT    36000    # Melbourne, Australia (not in IANA database)
NZT     43200    # New Zealand Time (not in IANA database)
SADT    37800 D  # South Australian Daylight-Saving Time (not in IANA database)
# CONFLICT! SAST is not unique
# Other timezones:
#  - SAST South Africa Standard Time
SAST    34200    # South Australian Standard Time (not in IANA database)
SAT     34200    # South Australian Standard Time (not in IANA database)
WADT    28800 D  # West Australian Daylight-Saving Time (not in IANA database)
WAST    25200    # West Australian Standard Time (not in IANA database)
WDT     32400 D  # West Australian Daylight-Saving Time (not in IANA database)
# CONFLICT! WST is not unique
# Other timezones:
#  - WST: West Samoa Time
WST     28800    # Western Standard Time (not in IANA database)
