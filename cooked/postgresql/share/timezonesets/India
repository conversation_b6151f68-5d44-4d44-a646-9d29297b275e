# Time zone configuration file for set "India"

# In order to use this file, you need to set the run-time parameter
# timezone_abbreviations to 'India'.  See the `Date/Time Support'
# appendix in the PostgreSQL documentation for more information.
#
# src/timezone/tznames/India


# include the default set
@INCLUDE Default

# most timezones are already defined in the default set. With the OVERRIDE
# option, PostgreSQL will use the new definitions instead of throwing an error
# in case of a conflict.
@OVERRIDE

IST     19800    # Indian Standard Time
                 #     (Asia/Calcutta)
