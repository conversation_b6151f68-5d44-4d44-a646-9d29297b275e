from typing import List, Dict, Any, Optional
from huey import crontab, RedisHuey
from storage.util.enum import ActivityStatus
from ddr.service.unified_event_processor import UnifiedDDREventProcessor
from ddr.model.task import get_ddr_tasks_dict
from datetime import datetime, timedelta, timezone
from util.common_log import get_logger
from util.config import configs

logger = get_logger("ddr")

ddr_huey = RedisHuey("ddr_task", db=configs["huey"]["db"])
task_interval = configs["ddr_task"]["task_interval"]


@ddr_huey.task()
def process_ddr_events(task: dict,
                       start_time: datetime, end_time: datetime,
                       target_operations: Optional[List[str]] = None,
                       scan_trigger_events: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    Optimized DDR event processing task.

    Args:
        task: DDRTask dict
        start_time: Start time for event processing
        end_time: End time for event processing
        target_operations: List of target operation types
        scan_trigger_events: List of scan trigger event types
    Returns:
        Result dictionary
    """
    try:
        logger.info(f"Starting optimized DDR event processing for task: {task['name']} "
                    f"start_time: {start_time.isoformat()}, end_time: {end_time.isoformat()}")

        # Use unified event processor
        event_processor = UnifiedDDREventProcessor(task)

        # Process platform events
        result = event_processor.process_events(
            start_time=start_time,
            end_time=end_time,
            status=ActivityStatus.NEW,
            target_operations=target_operations,
            scan_trigger_events=scan_trigger_events
        )

        logger.info(f"Optimized DDR event processing completed for task {task['name']}: {result}")
        return result

    except Exception as e:
        logger.error(f"Error in optimized DDR event processing task: {e}")
        return {"error": str(e), "processed": 0, "scans_triggered": 0}


# Optimized DDR task scheduler - runs every 5 minutes instead of every minute
@ddr_huey.periodic_task(crontab(minute='*/5'))
def optimized_ddr_dispatcher():
    """
    Optimized DDR task scheduler:
    - Runs every 5 minutes instead of every minute
    - Batches processing for better efficiency
    - Only processes tasks with actual activities
    """
    now = datetime.now(timezone.utc)
    try:
        tasks = get_ddr_tasks_dict(enabled=True)
        logger.info(f"Found {len(tasks)} active DDR tasks for optimized processing")
    except Exception as e:
        logger.error(f"Failed to fetch active DDR tasks: {e}")
        return

    # Batch process tasks to reduce overhead
    processed_count = 0
    for task in tasks:
        try:
            # Use 5-minute interval for checking
            last = now - timedelta(minutes=5)

            # Check if there are activities for this storage in the time window
            if not _has_recent_activity(task["storage_id"], last, now, task["scan_folders"]):
                logger.debug(f"No recent activities for task {task['name']}, skipping")
                continue

            # Schedule processing with slight delay to spread load
            process_ddr_events.schedule(
                args=(task, last, now),
                kwargs={
                    'scan_trigger_events': task.get('trigger_events', [])
                },
                delay=processed_count * 2  # Stagger tasks by 2 seconds each
            )

            processed_count += 1
            logger.info(f"Scheduled DDR processing for task {task['name']}")

        except Exception as e:
            logger.error(f"Error scheduling DDR task {task['name']}: {e}")

    if processed_count > 0:
        logger.info(f"Scheduled {processed_count} DDR tasks for processing")


def _has_recent_activity(storage_id: str, start_time: datetime, end_time: datetime,
                        scan_folders: List[str]) -> bool:
    """
    Check if there are recent activities for a storage

    Args:
        storage_id: Storage ID to check
        start_time: Start time for checking
        end_time: End time for checking
        scan_folders: Folders to check for activities

    Returns:
        bool: True if there are recent activities
    """
    try:
        from storage.model.activity import has_activity
        return has_activity(
            storage_id=storage_id,
            status=ActivityStatus.NEW,
            created_at__gte=start_time,
            created_at__lt=end_time,
            ddr_fields__jsonb__filter=scan_folders
        )
    except Exception as e:
        logger.error(f"Error checking for recent activity: {e}")
        return False
