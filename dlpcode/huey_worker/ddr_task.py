from typing import List, Dict, Any, Optional
from huey import crontab, RedisHuey
from storage.util.enum import ActivityStatus
from ddr.service.process_service import DDRProcessService, has_activity
from ddr.model.task import get_ddr_tasks_dict
from datetime import datetime, timedelta, timezone
from util.common_log import get_logger
from util.config import configs

logger = get_logger("ddr")

ddr_huey = RedisHuey("ddr_task", db=configs["huey"]["db"])
task_interval = configs["ddr_task"]["task_interval"]


@ddr_huey.task()
def process_ddr_events(task: dict, 
                       start_time: datetime, end_time: datetime,
                       target_operations: Optional[List[str]] = None,
                       scan_trigger_events: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    Task for processing DDR events.
    
    Args:
        task: DDRTask dict
        target_operations: List of target operation types
        scan_trigger_events: List of scan trigger event types
    Returns:
        Result dictionary
    """
    try:
        logger.info(f"Starting DDR event processing for task: {task['name']} "
                    f"start_time: {start_time.isoformat()}, end_time: {end_time.isoformat()}")

        ddr_service = DDRProcessService(task)
        # Process platform events
        result = ddr_service.process_events(
            start_time=start_time,
            end_time=end_time,
            status=ActivityStatus.NEW,
            target_operations=target_operations,
            scan_trigger_events=scan_trigger_events
        )

        logger.info(f"DDR event processing completed for task {task['name']}: {result}")
        return result

    except Exception as e:
        logger.error(f"Error in DDR event processing task: {e}")
        return {"error": str(e), "processed": 0, "scans_triggered": 0}


# Universal schedule: check all active DDR tasks and run if interval reached
@ddr_huey.periodic_task(crontab(minute='*/{task_interval}'))
def scheduled_ddr_dispatcher():
    """
    Universal DDR task scheduler: checks all active DDR tasks and runs those whose interval has elapsed.
    """
    now = datetime.now(timezone.utc)
    try:
        tasks = get_ddr_tasks_dict(enabled=True)
        logger.info(f"Found {len(tasks)} active DDR tasks")
    except Exception as e:
        logger.error(f"Failed to fetch active DDR tasks: {e}")
        return

    for task in tasks:
        try:
            last = now - timedelta(minutes=task_interval)
            if not has_activity(task["storage_id"], last, now, task["scan_folders"]):
                logger.info(f"No new activities for task {task['name']}, "
                            f"start_time: {last.isoformat()}, end_time: {now.isoformat()}, skip")
                continue

            process_ddr_events.schedule(
                args=(task, last, now),
                kwargs={
                    'scan_trigger_events': task.get('trigger_events', [])
                },
                delay=1
            )

        except Exception as e:
            logger.error(f"Error processing DDR task {task['name']}: {e}")
