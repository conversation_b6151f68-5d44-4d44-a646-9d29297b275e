import functools
import json
import shutil
import time
from connector.interface import <PERSON><PERSON>orInter<PERSON>
from datetime import datetime
from domain_model.huey_task_backlog import HueyTaskBacklog
from domain_model.huey_resume_backlog import <PERSON><PERSON>ResumeBacklog
from exts import logger, SessionExpired, get_logger, rs_client
from huey import RedisHuey, PriorityRedisHuey
from pathlib import Path
from service.huey_queue_management_service import HueyQueueManagementService
from service.task_queue_service import TaskQueueService
from service.process_management_service import ProcessManagementService
from typing import Union, Tuple, Dict
from util.config import configs
from util.enum_ import TaskStatus, ScanResult, ScanMethod
from util.utils import get_utc_timestamp, get_beginning_of_utc_timestamp
from system.system_log import record_event_log, LogLevel, LogAction, LogType
from util.config import get_scan_config
from ddr.model.task import get_ddr_task
from ddr.model.connector_builder import DDRConnectorBuilder

skip_files = get_logger("skip_files")

def check_session_expired(func):
    """
    Decorator to check if the session has expired before executing the function.
    If the session has expired, it raises a SessionExpired exception.
    """

    @functools.wraps(func)
    def wrapper(self, *args, **kwargs):
        if not self.session_tracker.is_alive():
            raise SessionExpired()
        return func(self, *args, **kwargs)

    return wrapper


class DDRTaskMgmtService:

    def __init__(self, ddr_task_id: str, session_key: str = ""):
        try:
            self.ddr_task_id = ddr_task_id
            self.ddr_download_worker_tracker = DDRDownloadWorkerTracker(ddr_task_id)
            self.ddr_analyze_worker_tracker = DDRAnalyzeWorkerTracker(ddr_task_id)
            self.task_backlog = TaskQueueService()
            self.scan_interval = -1.0
            self.session_tracker = SessionTracker(ddr_task_id, session_key)
            self.download_status_tracker = DDRDownloadStatusTracker(ddr_task_id, self.session_tracker.get_session_key())
            self.task_tracker = TaskTracker(ddr_task_id, self.session_tracker.get_session_key())
            self.process_management_service = ProcessManagementService(ddr_task_id)
        except Exception as e:
            logger.error(e)

    def is_running(self):
        """
        Checks if the task management service is currently running.

        Returns:
            bool: True if the service is running, False otherwise.
        """
        if not self.session_tracker.is_alive():
            return False

        try:
            results = [
                self.is_scanning()
            ]
            return any(results)
        except Exception as e:
            logger.error(e)
            return False

    def is_stopping(self):
        """
        Checks if the task is in the stopping state.
        The stopping status indicates that the task is performing cleanup work.

        Returns:
            bool: True if the task is in the stopping state, False otherwise.
        """
        try:
            scan_policy = get_ddr_task(id=self.ddr_task_id)
            if scan_policy is None:
                return False
            return not scan_policy.enabled
        except Exception as e:
            logger.error(e)
            return False

    def is_scanning(self) -> bool:
        """
        Checks if the task is in scanning status.

        Returns:
            bool: True if the task is in scanning status, False otherwise.

        Note:
            The methods is_scanning(), is_preparing(), and is_fetching() will not raise a SessionExpired exception.
            Instead, they will return False if the session has expired.
        """
        try:
            if not self.session_tracker.is_alive():
                return False

            ddr_task = get_ddr_task(id=self.ddr_task_id)
            if ddr_task is None:
                return False
            return ddr_task.enabled
        except Exception as e:
            logger.error(e)
            return False

    def get_connector(self) -> Union[None, ConnectorInterface]:
        """
        Get the connector for the ddr task.

        Returns:
            The connector object.
        """
        try:
            scan_task = get_ddr_task(id=self.ddr_task_id)
            if scan_task is None:
                return None
            return DDRConnectorBuilder(scan_task, self.session_tracker.get_session_key()).get_connector()

        except Exception as e:
            logger.error(e)
            return None

    @check_session_expired
    def init_trackers(self, total_count: int = 0) -> bool:
        """
        Initializes the trackers for the task.

        Args:
            total_count (int): The total count of the files.

        Returns:
            bool: True if the trackers were successfully initialized. False otherwise.

        Raises:
            SessionExpired: If the session is expired (due to @check_session_expired decorator).
        """
        session_key = self.session_tracker.get_session_key()
        try:
            result = [
                self.download_status_tracker.init(),
                self.download_status_tracker.set_total(total_count),
                self.ddr_analyze_worker_tracker.init(session_key),
                self.ddr_analyze_worker_tracker.set_total(session_key, total_count),
            ]
            return all(result)
        except Exception as e:
            logger.error(e)
            return False

    @check_session_expired
    def init_global_trackers(self, total_group_count: int = 0) -> bool:
        """
        Initializes the trackers for the task.

        Args:
            total_group_count (int): The total count of the groups.

        Returns:
            bool: True if the trackers were successfully initialized. False otherwise.

        Raises:
            SessionExpired: If the session is expired (due to @check_session_expired decorator).
        """
        try:
            result = [
                self.task_tracker.init(),
                self.task_tracker.set_group_total(total_group_count),
            ]
            return all(result)
        except Exception as e:
            logger.error(e)
            return False

    @check_session_expired
    def set_trackers_for_resume(self, current_count: int, ignored_count: int, dispatch_count: int, total_count: int, group_total: int, group_count: int, scan_total:int, scan_current_count:int, scan_ignore_count) -> bool:
        """
        Sets the trackers for resuming a task.

        Args:
            current_count (int): The number of items that have been processed.
            ignored_count (int): The number of items that have been ignored.
            dispatch_count (int): The number of items that have been dispatched.
            total_count (int): The total number of items in the task.
            group_count (int): The number of groups that have been processed.
            group_total (int): The total number of groups in the task.
            scan_total (int): The total number of files in the task.
            scan_current_count (int): The number of files that have been processed in the task.
            scan_ignore_count (int): The number of files that have been ignored in the task.

        Returns:
            bool: True if the trackers were successfully set for resuming, False otherwise.

        Raises:
            SessionExpired: If the session is expired (due to @check_session_expired decorator).
        """
        session_key = self.session_tracker.get_session_key()
        try:
            result = [
                self.download_status_tracker.init(),
                self.download_status_tracker.set_total(total_count),
                self.ddr_analyze_worker_tracker.init(session_key),
                self.ddr_analyze_worker_tracker.set_resume_counter(session_key, current_count, ignored_count, total_count),
                self.task_tracker.set_resume_counter(group_total, group_count, scan_total, scan_current_count, scan_ignore_count),
            ]
            return all(result)
        except Exception as e:
            logger.error(e)
            return False

    def purge_resume_metadata(self) -> bool:
        """
        Purges the resume metadata for the current ddr task.

        This method performs the following operations:
        1. Deletes scan progress entries for the current ddr task.
        2. Deletes resume backlog entries for the current ddr task.
        3. Deletes the file info queue for the current ddr task.

        Returns:
            bool: True if the resume metadata was successfully purged, False otherwise.
        """
        from domain_model.scan_progress import ScanProgress
        from exts import Session

        try:
            with Session() as session:
                session.query(ScanProgress).filter(ScanProgress.ddr_task_id == self.ddr_task_id).delete()
                session.query(HueyResumeBacklog).filter(HueyResumeBacklog.ddr_task_id == self.ddr_task_id).delete()
                session.commit()

            file_path = Path(configs["backlog_dir"]) / f"file_info_{self.ddr_task_id}"
            file_path.unlink(missing_ok=True)

            group_file_path = Path(configs["backlog_dir"]) / f"group_info_{self.ddr_task_id}"
            group_file_path.unlink(missing_ok=True)
            return True
        except OSError as e:
            logger.error(e)
            return False
        except Exception as e:
            logger.error(e)
            return False

    def generate_current_statistics_info(self) -> str:
        try:
            group_counter = self._get_analyze_counter()
            current_group = self.get_current_group_progress()
            #scan_total_count = self.get_file_total()
            scan_current_count = self.get_file_counter()
            scan_ignored_count = self.get_file_ignored_counter()
            group_total = self.get_group_total()
            statistics_info = f"Total Group: {group_total}, Analyzed Group: {current_group}, Current Group Total Files: {group_counter.get('total', 0)}, Current Group Analyzed Files: {group_counter.get('current', 0)}, Current Group Skipped Files: {group_counter.get('ignored', 0)}, Total Analyzed Files: {scan_current_count}, Total Skipped Files: {scan_ignored_count}"
            return statistics_info
        except Exception as e:
            logger.error(e)
            return ""

    def prepare_resume_metadata(self) -> bool:
        """
        Prepares the resume metadata for the current ddr task.

        This method performs the following operations:
        1. Copies the backlog to the resume backlog.
        2. Deletes existing scan progress entries for the current ddr task.
        3. Retrieves the total count from the HueyTaskCounter.
        4. Prepares and saves new scan progress entries.
        5. Copies the file info queue.

        Returns:
            bool: True if the resume metadata was successfully prepared, False otherwise.
        """
        from domain_model.scan_progress import ScanProgress
        from domain_model.huey_task_backlog import HueyTaskBacklog
        from domain_model.huey_resume_backlog import HueyResumeBacklog
        from domain_model.huey_task_counter import HueyTaskCounter
        from sqlalchemy import and_
        from exts import Session

        try:
            session_key = self.session_tracker.get_session_key()
            with Session() as session:
                session.query(HueyResumeBacklog).filter_by(ddr_task_id=self.ddr_task_id).delete()
                session.query(ScanProgress).filter_by(ddr_task_id=self.ddr_task_id).delete()
                session.commit()

                # Use with_for_update() to lock the records to prevent race conditions
                with session.begin():
                    total_count = -1
                    current_count = -1
                    ignored_count = -1
                    counter_records = (
                        session.query(HueyTaskCounter)
                        .with_for_update()
                        .filter_by(
                            ddr_task_id=self.ddr_task_id,
                            session_key=session_key,
                        )
                        .all()
                    )
                    for record in counter_records:
                        if record.namespace == self.ddr_analyze_worker_tracker.total_key:
                            total_count = record.value
                        elif record.namespace == self.ddr_analyze_worker_tracker.counter_key:
                            current_count = record.value
                        elif record.namespace == self.ddr_analyze_worker_tracker.ignored_counter_key:
                            ignored_count = record.value

                    current = self.get_dispatch_current_progress()
                    current_group = self.get_current_group_progress()
                    scan_total_count = self.get_file_total()
                    scan_current_count = self.get_file_counter()
                    scan_ignored_count = self.get_file_ignored_counter()
                    group_total = self.get_group_total()
                    scan_method = self.task_tracker.get_scan_method()
                    objects = [
                        ScanProgress(
                            ddr_task_id=self.ddr_task_id,
                            session_key=session_key,
                            total_count=total_count,
                            current_count=current_count,
                            ignore_count=ignored_count,
                            dispatch_count=current,
                            current_group_counter=current_group,
                            group_total=group_total,
                            scan_total_count=scan_total_count,
                            scan_current_count=scan_current_count,
                            scan_ignore_count=scan_ignored_count,
                            scan_method=scan_method,
                            created_at=get_utc_timestamp(),
                        )
                    ]

                    filters = [
                        HueyTaskBacklog.ddr_task_id == self.ddr_task_id,
                        HueyTaskBacklog.session_key == session_key,
                        HueyTaskBacklog.dispatch_count < current,
                    ]
                    backlog_records = session.query(HueyTaskBacklog).filter(and_(*filters)).all()
                    for record in backlog_records:
                        objects.append(
                            HueyResumeBacklog(
                                ddr_task_id=self.ddr_task_id,
                                session_key=record.session_key,
                                file_info=record.file_info,
                                backlog_hash=record.backlog_hash,
                                params=record.params,
                                dispatch_count=record.dispatch_count,
                                created_at=record.created_at,
                            )
                        )
                    session.bulk_save_objects(objects)

            self.copy_file_info_queue()
            return True
        except Exception as e:
            logger.error(e)
            return False

    @check_session_expired
    def restore_resume_backlogs(self) -> bool:
        """
        Restores resume backlogs for the current scan policy.

        This method retrieves all HueyResumeBacklog records for the current scan policy,
        and creates new HueyTaskBacklog records based on the retrieved HueyResumeBacklog records.
        The new HueyTaskBacklog records are associated with the current session.

        Returns:
            bool: True if resume backlogs were successfully restored, False otherwise.

        Raises:
            SessionExpired: If the session is expired (due to @check_session_expired decorator).
        """
        from exts import Session

        try:
            session_key = self.session_tracker.get_session_key()
            with Session() as session:
                objects = []
                records = session.query(HueyResumeBacklog).filter_by(ddr_task_id = self.ddr_task_id).all()
                for record in records:
                    backlog = HueyTaskBacklog(
                        ddr_task_id=self.ddr_task_id,
                        session_key=session_key,
                        file_info=record.file_info,
                        backlog_hash=record.backlog_hash,
                        params=record.params,
                        dispatch_count=record.dispatch_count,
                        created_at=record.created_at,
                    )
                    objects.append(backlog)
                session.bulk_save_objects(objects)
                session.commit()

                session.query(HueyResumeBacklog).filter_by(ddr_task_id = self.ddr_task_id).delete()
                session.commit()
            return True
        except Exception as e:
            logger.error(e)
            return False

    def copy_file_info_queue(self) -> bool:
        """
        Copies the file info queue for the current scan policy.

        Returns:
            bool: True if the file info queue was successfully copied, False otherwise.
        """
        try:
            backlog_dir = Path(configs["backlog_dir"])
            backlog_dir.mkdir(exist_ok=True)
            backlog_file_path = backlog_dir / f"file_info_{self.ddr_task_id}"
            backlog_file_path.unlink(missing_ok=True)

            file_path = (
                Path(configs["download_queue"]["file_info_queue_path"])
                / f"file_info_{self.ddr_task_id}_{self.session_tracker.get_session_key()}"
            )
            if file_path.exists():
                shutil.copy(file_path, backlog_file_path)

            file_path.unlink(missing_ok=True)

            backlog_group_file_path = backlog_dir / f"group_info_{self.ddr_task_id}"
            backlog_group_file_path.unlink(missing_ok=True)
            group_file_path = (
                Path(configs["download_queue"]["file_info_queue_path"])
                / f"group_info_{self.ddr_task_id}_{self.session_tracker.get_session_key()}"
            )
            if group_file_path.exists():
                shutil.copy(group_file_path, backlog_group_file_path)
            group_file_path.unlink(missing_ok=True)

        except KeyError as e:
            logger.error(f"KeyError occurred: {e}")
            return False
        except OSError as e:
            logger.error(e)
            return False
        except Exception as e:
            logger.error(e)
            return False


    @check_session_expired
    def get_file_total(self) -> int:
        """
        Get the number of total files.

        Returns:
            int: The number of total files.

        Raises:
            SessionExpired: If the session is expired (due to @check_session_expired decorator).
        """
        try:
            return self.task_tracker.get_file_total()
        except Exception as e:
            logger.error(e)
            return -1

    @check_session_expired
    def set_file_total(self, total) -> bool:
        """
        Set the number of total files.

        Returns:
            bool: True if the set operation is successful, False otherwise.

        Raises:
            SessionExpired: If the session is expired (due to @check_session_expired decorator).
        """
        try:
            return self.task_tracker.set_file_total(total)
        except Exception as e:
            logger.error(e)
            return False

    @check_session_expired
    def get_file_counter(self) -> int:
        """
        Get the number of scanned files.

        Returns:
            int: The number of scanned files.

        Raises:
            SessionExpired: If the session is expired (due to @check_session_expired decorator).
        """
        try:
            return self.task_tracker.get_file_counter()
        except Exception as e:
            logger.error(e)
            return -1

    @check_session_expired
    def set_file_counter(self, file_counter) -> bool:
        """
        Set the number of scanned files.

        Returns:
            bool: True if the set operation is successful, False otherwise.

        Raises:
            SessionExpired: If the session is expired (due to @check_session_expired decorator).
        """
        try:
            return self.task_tracker.set_file_counter(file_counter)
        except Exception as e:
            logger.error(e)
            return False

    @check_session_expired
    def get_file_ignored_counter(self) -> int:
        """
        Get the number of ignored files.

        Returns:
            int: The number of ignored files.

        Raises:
            SessionExpired: If the session is expired (due to @check_session_expired decorator).
        """
        try:
            return self.task_tracker.get_file_ignored_counter()
        except Exception as e:
            logger.error(e)
            return -1

    @check_session_expired
    def set_file_ignored_counter(self, file_ignored_counter) -> bool:
        """
        Set the number of ignored files.

        Returns:
            bool: True if the set operation is successful, False otherwise.

        Raises:
            SessionExpired: If the session is expired (due to @check_session_expired decorator).
        """
        try:
            return self.task_tracker.set_file_ignored_counter(file_ignored_counter)
        except Exception as e:
            logger.error(e)
            return False


    @check_session_expired
    def update_start_time(self) -> bool:
        """
        Updates the start time of the task.

        Returns:
            bool: True if the start time was successfully updated, False otherwise.

        Raises:
            SessionExpired: If the session is expired (due to @check_session_expired decorator).
        """
        try:
            return self.task_tracker.update_start_time()
        except Exception as e:
            logger.error(e)
            return False

    @check_session_expired
    def update_running_time(self) -> bool:
        """
        Updates the running time of the task.

        Returns:
            bool: True if the running time was successfully updated, False otherwise.

        Raises:
            SessionExpired: If the session is expired (due to @check_session_expired decorator).
        """
        try:
            return self.task_tracker.update_running_time()
        except Exception as e:
            logger.error(e)
            return False

    @check_session_expired
    def update_cutoff_time(self, cutoff_time: datetime) -> bool:
        """
        Updates the cutoff time for the scan task.

        Args:
            cutoff_time (datetime): The new cutoff time for the scan task.

        Returns:
            bool: True if the cutoff time was successfully updated, False otherwise.

        Raises:
            SessionExpired: If the session is expired (due to @check_session_expired decorator).
        """
        try:
            return self.task_tracker.update_cutoff_time(cutoff_time)
        except Exception as e:
            logger.error(e)
            return False

    @check_session_expired
    def update_scan_method(self, scan_method: int) -> bool:
        """
        Updates the scan method of the task.

        Args:
            scan_method (int): The scan method.

        Returns:
            bool: True if the scan method was successfully updated, False otherwise.

        Raises:
            SessionExpired: If the session is expired (due to @check_session_expired decorator).
        """
        try:
            return self.task_tracker.update_scan_method(ScanMethod(scan_method))
        except Exception as e:
            logger.error(e)
            return False

    @check_session_expired
    def get_cutoff_time(self) -> Union[datetime, None]:
        """
        Retrieves the cutoff time for the scan task.
        If the cutoff time is not found, the beginning of the UTC timestamp is returned.

        Returns:
            datetime: The cutoff time for the task.

        Raises:
            SessionExpired: If the session is expired (due to @check_session_expired decorator).
        """
        try:
            return self.task_tracker.get_cutoff_time()
        except Exception as e:
            logger.error(e)


    @check_session_expired
    def increment_ignored_counter(self, session_key: str) -> bool:
        """
        Increments the ignored counter for a specific session.

        Args:
            session_key (str): The unique identifier for the session.

        Returns:
            bool: True if the ignored counter was successfully incremented, False otherwise.

        Raises:
            SessionExpired: If the session is expired (due to @check_session_expired decorator).
        """
        try:
            self.update_running_time()
            return self.analyze_worker_tracker.increment_ignored_counter(session_key)
        except Exception as e:
            logger.error(e)
            return False

    @check_session_expired
    def start_download_track(self, params: dict, backlog_hash: str) -> bool:
        """
        Starts a download track for a given set of parameters and backlog hash.

        Args:
            params (dict): A dictionary containing the parameters for the download track.
            backlog_hash (str): The backlog hash associated with the download track.

        Returns:
            bool: True if the download track was successfully started, False otherwise.

        Raises:
            SessionExpired: If the session is expired (due to @check_session_expired decorator).
        """
        try:
            self.update_running_time()
            session_key = self.session_tracker.get_session_key()
            return self.doing_download_task_tracker.start_track(session_key, params, backlog_hash)
        except Exception as e:
            logger.error(e)
            return False

    @check_session_expired
    def end_download_track(self, backlog_hash: str, error: bool) -> bool:
        """
        Ends the download tracking for a specific backlog hash.

        Args:
            backlog_hash (str): The hash of the backlog being tracked.
            error (bool): Indicates whether an error occurred during the download.

        Returns:
            bool: True if the download tracking was successfully ended, False otherwise.

        Raises:
            SessionExpired: If the session is expired (due to @check_session_expired decorator).
        """
        try:
            self.update_running_time()
            session_key = self.session_tracker.get_session_key()
            if not error:
                return self.doing_download_task_tracker.end_track(session_key, backlog_hash)
            else:
                return self.doing_download_task_tracker.end_track_with_error(session_key, backlog_hash)
        except Exception as e:
            logger.error(e)
            return False

    @check_session_expired
    def start_analyze_track(self, params: dict, backlog_hash: str) -> bool:
        """
        Starts the analysis track process.

        Args:
            params (dict): A dictionary containing the parameters for the analysis track.
            backlog_hash (str): The backlog hash for the analysis track.

        Returns:
            bool: True if the analysis track was started successfully, False otherwise.

        Raises:
            SessionExpired: If the session is expired (due to @check_session_expired decorator).
        """
        try:
            self.update_running_time()
            session_key = self.session_tracker.get_session_key()
            return self.analyze_worker_tracker.start_track(session_key, params, backlog_hash)
        except Exception as e:
            logger.error(e)
            return False

    @check_session_expired
    def end_analyze_track(self, backlog_hash: str) -> bool:
        """
        Ends the tracking of an analysis task.

        Args:
            backlog_hash (str): The hash of the backlog item.

        Returns:
            bool: True if the tracking was successfully ended, False otherwise.

        Raises:
            SessionExpired: If the session is expired (due to @check_session_expired decorator).
        """
        try:
            self.update_running_time()
            session_key = self.session_tracker.get_session_key()
            return self.analyze_worker_tracker.end_track(session_key, backlog_hash)
        except Exception as e:
            logger.error(e)
            return False

    @check_session_expired
    def scan_backoff(self):
        """
        Pauses the scan process for a duration specified by the _get_scan_backoff_time() method.

        Raises:
            SessionExpired: If the session is expired (due to @check_session_expired decorator).
        """
        try:
            time.sleep(self._get_scan_backoff_time())
        except Exception as e:
            logger.error(e)

    @check_session_expired
    def download_error_threshold_reached(self) -> bool:
        """
        Checks if the error threshold has been reached.

        Returns:
            bool: True if the error threshold has been reached, False otherwise.

        Raises:
            SessionExpired: If the session is expired (due to @check_session_expired decorator).
        """
        try:
            return self.download_status_tracker.error_threshold_reached()
        except Exception as e:
            logger.error(e)
            return False

    @check_session_expired
    def increment_conn_error_count(self, has_error: bool = False) -> bool:
        """
        Increments the download progress and updates the connection error status.

        Args:
            has_error (bool, optional): Indicates if there was a connection error during the download.
                Defaults to False.

        Returns:
            bool: True if the connection error count was successfully set, False otherwise.

        Raises:
            SessionExpired: If the session is expired (due to @check_session_expired decorator).
        """
        try:
            return self.download_status_tracker.has_conn_error(has_error)
        except Exception as e:
            logger.error(e)
            return False

    def is_scan_finished(self) -> bool:
        """
        Checks if the scan is finished.

        Returns:
            bool: True if the scan is finished, False otherwise.

        Note:
            This method will not raise a SessionExpired exception.
            Instead, it will return False if the session has expired.
        """
        try:
            session_key = self.session_tracker.get_session_key()
            result = [
                self.analyze_worker_tracker.is_done(session_key),
                self.session_tracker.is_alive(),
            ]
            return all(result)
        except Exception as e:
            logger.error(e)
            return False


    @check_session_expired
    def create_backlog(self, file_info: dict, backlog_hash: str, params: dict, dispatch_count: int) -> bool:
        """
        Creates a new backlog entry in the database.

        Args:
            file_info (dict): A dictionary containing information about the file to be processed.
            backlog_hash (str): A unique hash identifying this backlog entry.
            params (dict): Additional parameters for the task, which will be pickled before storage.
            dispatch_count (int): Current dispatch count

        Returns:
            bool: True if the backlog entry was successfully created and committed to the database,
                  False if an exception occurred during the process.

        Raises:
            SessionExpired: If the session is expired (due to @check_session_expired decorator).
        """
        from exts import Session
        from psycopg2.errors import UniqueViolation

        try:
            with Session() as session:
                backlog = HueyTaskBacklog(
                    ddr_task_id=self.ddr_task_id,
                    session_key=self.session_tracker.get_session_key(),
                    file_info=file_info,
                    backlog_hash=backlog_hash,
                    params=json.dumps(params).encode('utf-8'),
                    dispatch_count=dispatch_count,
                    created_at=get_utc_timestamp(),
                )
                session.add(backlog)
                session.commit()
            return True
        except UniqueViolation as e:
            logger.error(e)
            return False
        except Exception as e:
            logger.error(e)
            return False

    @check_session_expired
    def delete_backlogs(self) -> bool:
        """
        Deletes all backlog entries associated with the current ddr task.

        This method removes all HueyTaskBacklog objects from the database that are
        associated with the current ddr task.

        Returns:
            bool: True if the backlog entries were successfully deleted and the changes
                  were committed to the database, False if an exception occurred during
                  the process.

        Raises:
            SessionExpired: If the session is expired (due to @check_session_expired decorator).
        """
        from exts import Session

        try:
            with Session() as session:
                session.query(HueyTaskBacklog).filter(HueyTaskBacklog.ddr_task_id == self.ddr_task_id).delete()
                session.commit()

            backlog_file_path = Path(configs["backlog_dir"]) / f"file_info_{self.ddr_task_id}"
            backlog_file_path.unlink(missing_ok=True)
            backlog_group_file = Path(configs["backlog_dir"]) / f"group_info_{self.ddr_task_id}"
            backlog_group_file.unlink(missing_ok=True)
            return True
        except Exception as e:
            logger.error(e)
            return False

    def is_resumable(self) -> Tuple[bool, Union[str, Dict]]:
        """
        Checks if the current scan is resumable and returns relevant information.

        This method determines whether the current scan can be resumed by checking
        its status, progress, and available file list. It performs the following checks:
        1. Verifies if the scan is in a suspending status.
        2. Retrieves the latest scan progress.
        3. Locates the latest file list.

        Returns:
            Tuple[bool, Union[str, Dict]]: A tuple containing:
                - bool: True if the scan is resumable, False otherwise.
                - Union[str, Dict]:
                    - If resumable: A dictionary containing resume information with keys:
                        - "group_file_path": Path to the latest group file list.
                        - "file_path": Path to the latest file list.
                        - "total_count": Total number of items to be scanned.
                        - "current_count": Number of items already scanned.
                        - "ignored_count": Number of items ignored during the scan.
                        - "dispatch_count": Number of items dispatched for scanning.
                    - If not resumable: A string explaining why the scan cannot be resumed.
        """
        from exts import Session

        try:
            # if the scan is not in suspending status, it cannot be resumed
            if not self.is_suspending():
                logger.info("Not resumable, the scan is not in SUSPENDING status")
                return False, "The scan is not in SUSPENDING status."

            # if we don't have the latest progress, it cannot be resumed
            progress = self._latest_scan_progress()
            if not progress:
                logger.info("Not resumable, Cannot find the latest scan progress")
                return False, "Cannot find the latest scan progress."

            # if we don't have the latest group info list, it cannot be resumed
            group_info_path = self._latest_group_info()
            if not group_info_path:
                logger.info("Not resumable, Cannot find the latest group info")
                return False, "Cannot find the latest group info."

            file_path = ""
            # total_count != -999, means last scan is in dispatch phase;
            # if we don't have the latest file info list, it cannot be resumed
            if "total_count" in progress and progress.get("total_count") != -999:
                file_path = self._latest_file_list()
                if not file_path:
                    logger.info("Not resumable, Cannot find the latest file list")
                    return False, "Cannot find the latest file list."

                records_count = 0
                with Session() as session:
                    records_count = session.query(HueyResumeBacklog).filter_by(ddr_task_id=self.ddr_task_id).count()

                curr_count = progress["current_count"]
                ign_count = progress["ignored_count"]
                disp_count = progress["dispatch_count"]
                #logger.info(f"records_count {records_count}, curr_count {curr_count}, ign_count {ign_count}, disp_count {disp_count}")
                if records_count + curr_count + ign_count < disp_count:
                    logger.info(f"records_count {records_count} + curr_count {curr_count} + ign_count {ign_count} < disp_count {disp_count}")
                    return False, "resume backlog count doesn't match"

            resume_info = {
                "group_info_path": group_info_path,
                "file_path": file_path,
                "total_count": progress["total_count"],
                "current_count": progress["current_count"],
                "ignored_count": progress["ignored_count"],
                "dispatch_count": progress["dispatch_count"],
                "group_total": progress["group_total"],
                "current_group_counter": progress["current_group_counter"],
                "scan_total_count": progress["scan_total_count"],
                "scan_current_count": progress["scan_current_count"],
                "scan_ignore_count": progress["scan_ignore_count"],
                "scan_method": progress["scan_method"],
            }
            return True, resume_info
        except Exception as e:
            logger.error(e)
            return False, str(e)



    def _latest_file_list(self) -> Union[str, None]:
        """
        Retrieves the path to the latest file list for the current scan policy.

        Returns:
            Union[str, None]: The path to the latest file list as a string if it exists,
                            or None if the file is not found.
        """
        try:
            file_path = Path(configs["backlog_dir"]) / f"file_info_{self.ddr_task_id}"
            if file_path.exists():
                return str(file_path)
            return None
        except KeyError as e:
            logger.error(f"KeyError occurred: {e}")
            return None
        except Exception as e:
            logger.error(e)
            return None


    def _get_scan_backoff_time(self) -> float:
        """
        Retrieves the scan backoff time based on the scan interval.

        Returns:
            float: The scan backoff time.
        """
        if self.scan_interval != -1.0:
            return self.scan_interval

        scan_policy = get_ddr_task(id=self.ddr_task_id)
        if scan_policy is None:
            logger.error("ddr task not found.")
            return 0.0

        try:
            interval = 0
            if scan_policy.scan_interval == 1:
                interval = 0.01
            elif scan_policy.scan_interval == 2:
                interval = 0.02
            elif scan_policy.scan_interval == 3:
                interval = 0.0
            self.scan_interval = interval
            return self.scan_interval
        except Exception as e:
            logger.error(e)
            return 0.0

    def _get_scan_finish_wait_time(self) -> int:
        try:
            scan_configs = get_scan_config()
            if self.ddr_task_id in scan_configs:
                return scan_configs[self.ddr_task_id].get('finish_wait_time', 3)

            if 'global' in scan_configs:
                return scan_configs['global'].get('finish_wait_time', 3)

            return 3
        except Exception as e:
            logger.error(e)
            return 3

    def _handle_scan_finish(self, previous_status: TaskStatus, need_resume: bool = True) -> bool:
        """
        Handles the completion of a scan task.

        This method performs the following operations:
        1. Logs the previous status of the task.
        2. Prepares or purges resume metadata based on the task's status and the need for resumption.
        3. Revokes the session tracker.
        4. Revokes the task from the Huey queue.
        5. Signals the process management service.
        6. Cleans up files associated with the ddr task.

        Args:
            previous_status (TaskStatus): The previous status of the task.
            need_resume (bool, optional): Indicates whether resumption is needed. Defaults to True.

        Returns:
            bool: True if the scan finish handling was successful, False otherwise.
        """
        try:
            # --- FOR Resume ---
            logger.debug(f"!!!!!!!! previous_status: {previous_status} !!!!!!!!")
            if need_resume and previous_status == TaskStatus.SCANNING:
                logger.info(f"Scanning task {self.ddr_task_id} is in progress. Keeping resume metadata.")
                self.prepare_resume_metadata()
            elif not need_resume or previous_status in [TaskStatus.PREPARING, TaskStatus.FETCHING]:
                logger.info(f"Task {self.ddr_task_id} is not in progress. Purging resume metadata.")
                self.purge_resume_metadata()
            # --- FOR Resume ---

            self.session_tracker.revoke()
            HueyQueueManagementService().revoke_task(self.ddr_task_id)
            #Wait worker process automatically exit, minimize sending signals
            time.sleep(self._get_scan_finish_wait_time())
            self.process_management_service.signal_proccess()
            self._clean_up_files()
            return True
        except Exception as e:
            logger.error(e)
            return False

    def _clean_up_files(self) -> bool:
        """
        Clean up files and directories associated with the current ddr task.

        This method performs the following cleanup operations:
        1. Removes the target directory for file downloads.
        2. Deletes the queue file containing file information.
        3. Deletes the group queue file containing group information.

        Returns:
            bool: True if the cleanup was successful, False if any error occurred.
        """
        try:
            target_dir = Path(configs["download_queue"]["ddr_file_download_path"]) / self.ddr_task_id
            if target_dir.exists():
                shutil.rmtree(target_dir, ignore_errors=True)

            queue_file = (
                Path(configs["download_queue"]["file_info_queue_path"])
                / f"file_info_{self.ddr_task_id}_{self.session_tracker.get_session_key()}"
            )
            queue_file.unlink(missing_ok=True)

            queue_group_file = (
                Path(configs["download_queue"]["file_info_queue_path"])
                / f"group_info_{self.ddr_task_id}_{self.session_tracker.get_session_key()}"
            )
            queue_group_file.unlink(missing_ok=True)
            return True
        except KeyError as e:
            logger.error(f"KeyError occurred: {e}")
            return False
        except OSError as e:
            logger.error(e)
            return False
        except Exception as e:
            logger.error(e)
            return False

    def _get_analyze_counter(self) -> dict:
        """
        Retrieves the analysis counter information.
        """
        try:
            session_key = self.session_tracker.get_session_key()
            return {
                "total": self.ddr_analyze_worker_tracker.get_total_count(session_key),
                "current": self.ddr_analyze_worker_tracker.get_current_progress(session_key),
                "ignored": self.ddr_analyze_worker_tracker.get_ignored_count(session_key),
            }
        except Exception as e:
            logger.error(e)
            return {}
