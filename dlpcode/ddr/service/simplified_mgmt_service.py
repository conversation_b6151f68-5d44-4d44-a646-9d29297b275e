"""
Simplified DDR Task Management Service

This module provides a streamlined DDR task management service that replaces
the complex DDRTaskMgmtService with a much simpler and more efficient implementation.
"""

import json
import time
from datetime import datetime
from typing import Dict, Optional, Any
from connector.interface import ConnectorInterface
from exts import logger, get_logger
from util.config import configs
from util.enum_ import ScanResult, ScanMethod
from util.utils import get_utc_timestamp
from ddr.model.task import get_ddr_task
from ddr.model.connector_builder import DDRConnectorBuilder
from ddr.tracker.unified_ddr_tracker import DDRTaskTrackerFactory

skip_files = get_logger("skip_files")


class SimplifiedDDRTaskMgmt:
    """
    Simplified DDR Task Management Service
    
    This service provides essential DDR task management functionality without
    the overhead of multiple trackers, complex session management, and
    unnecessary state synchronization.
    """
    
    def __init__(self, ddr_task_id: str):
        """
        Initialize simplified DDR task management
        
        Args:
            ddr_task_id: DDR task ID
        """
        self.ddr_task_id = ddr_task_id
        self.tracker = DDRTaskTrackerFactory.get_tracker(ddr_task_id)
        self._connector = None
        self._task_config = None
        
        logger.debug(f"Initialized simplified DDR task management for {ddr_task_id}")
    
    def get_task_config(self) -> Optional[Dict[str, Any]]:
        """
        Get DDR task configuration
        
        Returns:
            dict: Task configuration or None if not found
        """
        if self._task_config is None:
            try:
                self._task_config = get_ddr_task(self.ddr_task_id)
                if self._task_config:
                    logger.debug(f"Loaded task config for {self.ddr_task_id}")
                else:
                    logger.warning(f"Task config not found for {self.ddr_task_id}")
            except Exception as e:
                logger.error(f"Failed to load task config: {e}")
                return None
        
        return self._task_config
    
    def get_connector(self) -> Optional[ConnectorInterface]:
        """
        Get connector for the DDR task
        
        Returns:
            ConnectorInterface: Connector instance or None if failed
        """
        if self._connector is None:
            try:
                task_config = self.get_task_config()
                if not task_config:
                    return None
                
                # Create DDR task object for connector builder
                from ddr.model.task import DDRTask
                ddr_task = DDRTask()
                for key, value in task_config.items():
                    if hasattr(ddr_task, key):
                        setattr(ddr_task, key, value)
                
                # Build connector
                connector_builder = DDRConnectorBuilder(ddr_task)
                self._connector = connector_builder.build()
                
                if self._connector:
                    logger.debug(f"Built connector for DDR task {self.ddr_task_id}")
                else:
                    logger.error(f"Failed to build connector for DDR task {self.ddr_task_id}")
                    
            except Exception as e:
                logger.error(f"Failed to create connector: {e}")
                return None
        
        return self._connector
    
    def init_task_tracking(self, total_files: int = 0) -> bool:
        """
        Initialize task tracking
        
        Args:
            total_files: Total number of files to process
            
        Returns:
            bool: True if successful
        """
        try:
            return self.tracker.init_task(total_files)
        except Exception as e:
            logger.error(f"Failed to initialize task tracking: {e}")
            return False
    
    def update_file_progress(self, file_path: str, stage: str, status: str, 
                           error_msg: Optional[str] = None) -> bool:
        """
        Update progress for a specific file
        
        Args:
            file_path: Path of the file being processed
            stage: Current processing stage (download, analyze, complete)
            status: Status (processing, success, failed, skipped)
            error_msg: Error message if status is failed
            
        Returns:
            bool: True if successful
        """
        try:
            return self.tracker.update_file_status(file_path, stage, status, error_msg)
        except Exception as e:
            logger.error(f"Failed to update file progress: {e}")
            return False
    
    def get_task_status(self) -> Dict[str, Any]:
        """
        Get current task status
        
        Returns:
            dict: Task status information
        """
        try:
            return self.tracker.get_task_status()
        except Exception as e:
            logger.error(f"Failed to get task status: {e}")
            return {"status": "error", "error": str(e)}
    
    def mark_task_complete(self, final_status: str = "completed") -> bool:
        """
        Mark task as complete
        
        Args:
            final_status: Final status (completed, failed, cancelled)
            
        Returns:
            bool: True if successful
        """
        try:
            return self.tracker.mark_task_complete(final_status)
        except Exception as e:
            logger.error(f"Failed to mark task complete: {e}")
            return False
    
    def should_process_file(self, file_info: Dict[str, Any]) -> bool:
        """
        Determine if a file should be processed based on DDR task configuration
        
        Args:
            file_info: File information
            
        Returns:
            bool: True if file should be processed
        """
        try:
            task_config = self.get_task_config()
            if not task_config:
                return False
            
            # Check file size limits
            file_size_limit = task_config.get("file_size_limit", {})
            max_size_mb = file_size_limit.get("max", 0)
            
            if max_size_mb > 0 and file_info.get("file_size", 0) > 0:
                file_size_mb = file_info["file_size"] / (1024 * 1024)
                if file_size_mb > max_size_mb:
                    logger.debug(f"File {file_info.get('file_name')} exceeds size limit")
                    return False
            
            # Check file type
            scan_file_types = task_config.get("scan_file_type", [])
            if scan_file_types:
                file_ext = file_info.get("file_ext", "").lower()
                if not file_ext:
                    # Extract extension from file name
                    file_name = file_info.get("file_name", "")
                    if "." in file_name:
                        file_ext = file_name.split(".")[-1].lower()
                
                if file_ext and file_ext not in scan_file_types:
                    logger.debug(f"File {file_info.get('file_name')} type not in scan list")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking if file should be processed: {e}")
            return False
    
    def get_scan_params(self, activity_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get scan parameters for file processing
        
        Args:
            activity_info: Activity information from storage platform
            
        Returns:
            dict: Scan parameters
        """
        try:
            task_config = self.get_task_config()
            if not task_config:
                return {}
            
            # Build scan info
            scan_info = task_config.get("analyze_setting", {}).copy()
            scan_info.update({
                "scan_uuid": self.ddr_task_id,
                "scan_name": task_config.get("name", "DDR Task"),
                "scan_created_at": task_config.get("created_at"),
                "scan_description": task_config.get("description", ""),
                "scan_file_type": task_config.get("scan_file_type", []),
                "ddr_policy": task_config.get("ddr_policy_ids", []),
                "activity_id": activity_info.get("id"),
                "is_ddr": True
            })
            
            # Build remote info based on storage type
            storage_type = task_config.get("storage_type")
            storage_id = task_config.get("storage_id")
            
            # Get storage profile
            from storage.service.profiles import get_storage_profile_by_id
            storage = get_storage_profile_by_id(storage_id)
            if not storage:
                logger.error(f"Storage profile not found: {storage_id}")
                return {}
            
            auth_info = storage.get("auth_info", {})
            
            remote_info = {
                "storage_type": storage_type,
                "storage_id": storage_id,
                "storage_name": storage.get("name", ""),
                "file": "",
                "file_name": ""
            }
            
            # Add storage-specific auth info
            if storage_type == 1:  # AWS
                scan_info["target"] = str(auth_info.get("region_name", "GLOBAL"))
                remote_info.update({
                    "type": "aws",
                    "key_id": auth_info.get("key_id", ""),
                    "access_key": auth_info.get("access_key", ""),
                    "cloudtrail_arn": auth_info.get("cloudtrail_arn", ""),
                    "Bucket": task_config.get("scan_folders", [])
                })
            elif storage_type == 2:  # SharePoint
                scan_info["target"] = str(auth_info.get("tenantid", "UNKNOWN"))
                remote_info.update({
                    "type": "sharepoint",
                    "usecredentials": auth_info.get("usecredentials", False),
                    "tenantid": auth_info.get("tenantid", ""),
                    "clientid": auth_info.get("clientid", ""),
                    "clientsecret": auth_info.get("clientsecret", ""),
                    "allow_ntlm": auth_info.get("allow_ntlm", False)
                })
            elif storage_type == 6:  # Google Drive
                scan_info["target"] = str(auth_info.get("customer_id", "UNKNOWN"))
                remote_info.update({
                    "type": "google",
                    "customer_id": auth_info.get("customer_id", ""),
                    "delegated_admin_email": auth_info.get("delegated_admin_email", "")
                })
            
            return {
                "remote_info": remote_info,
                "scan_info": scan_info
            }
            
        except Exception as e:
            logger.error(f"Failed to get scan parameters: {e}")
            return {}
    
    def cleanup(self) -> None:
        """Clean up resources"""
        try:
            # Clean up tracker
            self.tracker.cleanup_expired_data()
            DDRTaskTrackerFactory.cleanup_tracker(self.ddr_task_id)
            
            # Reset connector
            self._connector = None
            self._task_config = None
            
            logger.debug(f"Cleaned up DDR task management for {self.ddr_task_id}")
            
        except Exception as e:
            logger.error(f"Failed to cleanup DDR task management: {e}")
