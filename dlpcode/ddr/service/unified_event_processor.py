"""
Unified DDR Event Processor

This module provides a unified event processing service that replaces both
DDRService and DDRProcessService with a single, efficient implementation.
"""

import hashlib
from datetime import datetime
from typing import List, Dict, Any, Optional
from storage.util.enum import ActivityStatus
from storage.model.activity import update_activities_status
from util.config import configs, get_supported_file_type
from util.enum_ import StorageType, ScanMethod
from util.common_log import get_logger
from ddr.service.simplified_mgmt_service import SimplifiedDDRTaskMgmt

logger = get_logger("ddr")


class UnifiedDDREventProcessor:
    """
    Unified DDR Event Processor
    
    This class combines the functionality of DDRService and DDRProcessService
    into a single, streamlined event processing service.
    """
    
    def __init__(self, task_config: Dict[str, Any]):
        """
        Initialize the unified event processor
        
        Args:
            task_config: DDR task configuration
        """
        self.task_config = task_config
        self.ddr_task_id = task_config['id']
        self.storage_type = task_config['storage_type']
        self.storage_id = task_config['storage_id']
        self.scan_folders = task_config['scan_folders']
        self.analyze_setting = task_config['analyze_setting']
        
        # Get supported file types
        self.scan_file_types = get_supported_file_type(task_config.get('scan_file_type', []))
        
        # Initialize simplified management service
        self.mgmt_service = SimplifiedDDRTaskMgmt(self.ddr_task_id)
        
        logger.debug(f"Initialized unified DDR event processor for task {self.ddr_task_id}")
    
    def process_events(self,
                       start_time: datetime, 
                       end_time: datetime,
                       status: int = ActivityStatus.NEW,
                       target_operations: Optional[List[str]] = None,
                       scan_trigger_events: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Process DDR events for the specified time range
        
        Args:
            start_time: Start time for event processing
            end_time: End time for event processing
            status: Activity status to filter (default: NEW)
            target_operations: List of target operation types
            scan_trigger_events: List of scan trigger event types
            
        Returns:
            dict: Processing results
        """
        try:
            logger.info(f"Processing DDR events for task {self.ddr_task_id} "
                       f"from {start_time.isoformat()} to {end_time.isoformat()}")
            
            # 1. Get platform events
            activities = self._get_activities(start_time, end_time, status, target_operations)
            if not activities:
                logger.debug(f"No activities found for task {self.ddr_task_id}")
                return {"processed": 0, "scans_triggered": 0, "skipped": 0}
            
            # Initialize task tracking
            self.mgmt_service.init_task_tracking(len(activities))
            
            scan_count = 0
            skipped_count = 0
            
            # 2. Process each activity
            for activity in activities:
                try:
                    # Check if file should be skipped
                    should_skip, should_log = self._should_skip_activity(activity)
                    if should_skip:
                        if should_log:
                            activity["status"] = ActivityStatus.SKIPPED
                            skipped_count += 1
                        continue
                    
                    # Get trigger events from task config or parameter
                    trigger_events = scan_trigger_events or self.analyze_setting.get('trigger_events', [])
                    
                    # Check if scan should be triggered
                    if self._should_trigger_scan(activity, trigger_events):
                        activity["status"] = ActivityStatus.PROCESSING
                        activity["scan_triggered"] = True
                        scan_count += 1
                        
                        # Schedule file processing
                        self._schedule_file_processing(activity)
                    else:
                        activity["status"] = ActivityStatus.SKIPPED
                        activity["scan_triggered"] = False
                        skipped_count += 1
                        
                except Exception as e:
                    logger.error(f"Error processing activity {activity.get('id', 'unknown')}: {e}")
                    activity["status"] = ActivityStatus.PENDING
                    activity["scan_triggered"] = False
            
            # 3. Update activity statuses in database
            update_activities_status(activities, logger=logger)
            
            result = {
                "processed": len(activities),
                "scans_triggered": scan_count,
                "skipped": skipped_count
            }
            
            logger.info(f"DDR event processing completed for task {self.ddr_task_id}: {result}")
            return result
            
        except Exception as e:
            logger.error(f"Error in DDR event processing: {e}")
            return {"processed": 0, "scans_triggered": 0, "skipped": 0, "error": str(e)}
    
    def _get_activities(self, 
                       start_time: datetime, 
                       end_time: datetime,
                       status: int = ActivityStatus.NEW,
                       target_operations: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        Get activities from storage_activity table
        
        Args:
            start_time: Start time for filtering
            end_time: End time for filtering
            status: Activity status to filter
            target_operations: List of target operation types
            
        Returns:
            list: List of activity dictionaries
        """
        try:
            from storage.model.activity import get_fetched_activities
            
            activities = get_fetched_activities(
                storage_id=self.storage_id,
                status=status,
                created_at__gte=start_time,
                created_at__lt=end_time,
                ddr_fields__jsonb__filter=self.scan_folders
            )
            
            if not activities:
                return []
            
            # Convert to dictionaries
            activity_dicts = [activity.to_dict() for activity in activities]
            
            # Filter by target operations if specified
            if target_operations:
                activity_dicts = [
                    activity for activity in activity_dicts
                    if activity.get("event_type") in target_operations
                ]
            
            logger.debug(f"Retrieved {len(activity_dicts)} activities for processing")
            return activity_dicts
            
        except Exception as e:
            logger.error(f"Error retrieving activities: {e}")
            return []
    
    def _should_skip_activity(self, activity: Dict[str, Any]) -> tuple[bool, bool]:
        """
        Determine whether to skip an activity
        
        Args:
            activity: Activity data
            
        Returns:
            tuple: (should_skip, should_log)
        """
        try:
            # Check file-specific skip rules
            if self._should_skip_file(activity):
                return True, True
            
            # Storage-specific skip rules
            if self.storage_type == StorageType.AWS:
                if not activity.get("event_type"):
                    return True, False
            elif self.storage_type == StorageType.SHAREPOINT_OL:
                # Focus on configured folders
                site_url = activity.get("raw_data", {}).get("SiteUrl", "").strip("/")
                if site_url not in self.scan_folders:
                    return True, False
            elif self.storage_type == StorageType.GOOGLE:
                if not activity.get("event_type"):
                    return True, False
            
            return False, False
            
        except Exception as e:
            logger.error(f"Error checking skip rules: {e}")
            return False, False
    
    def _should_skip_file(self, activity: Dict[str, Any]) -> bool:
        """
        Check if file should be skipped based on DDR task configuration
        
        Args:
            activity: Activity data
            
        Returns:
            bool: True if file should be skipped
        """
        try:
            ddr_fields = activity.get("ddr_fields", {})
            
            # Check file size
            file_size_limit = self.task_config.get("file_size_limit", {})
            max_file_size_mb = file_size_limit.get("max", 0)
            
            if max_file_size_mb > 0 and ddr_fields.get("file_size"):
                file_size_mb = ddr_fields["file_size"] / (1024 * 1024)
                if file_size_mb > max_file_size_mb:
                    return True
            
            # Check file extension
            if self.scan_file_types:
                file_ext = ddr_fields.get("file_ext", "")
                if not file_ext:
                    # Extract from file name
                    file_name = ddr_fields.get("file_name", "")
                    if "." in file_name:
                        file_ext = file_name.split(".")[-1].lower()
                
                if file_ext and file_ext not in self.scan_file_types.get("ext", []):
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking file skip rules: {e}")
            return False
    
    def _should_trigger_scan(self, activity: Dict[str, Any], trigger_events: List[str]) -> bool:
        """
        Determine whether to trigger scan for an activity
        
        Args:
            activity: Activity data
            trigger_events: List of events that should trigger scans
            
        Returns:
            bool: True if scan should be triggered
        """
        if not trigger_events:
            return False
        
        event_type = activity.get("event_type", "")
        return event_type in trigger_events
    
    def _schedule_file_processing(self, activity: Dict[str, Any]) -> None:
        """
        Schedule file processing for an activity
        
        Args:
            activity: Activity data
        """
        try:
            # Build file info from activity
            file_info = self._build_file_info(activity)
            if not file_info:
                logger.warning(f"Could not build file info for activity {activity.get('id', '')}")
                activity["status"] = ActivityStatus.SKIPPED
                activity["scan_triggered"] = False
                return
            
            # Generate backlog hash
            backlog_content = f"{self.ddr_task_id}:{activity.get('id', '')}:{file_info.get('file_name', '')}"
            backlog_hash = hashlib.md5(backlog_content.encode()).hexdigest()
            
            # Get scan parameters
            params = self.mgmt_service.get_scan_params(activity)
            if not params:
                logger.error(f"Could not get scan parameters for activity {activity.get('id', '')}")
                activity["status"] = ActivityStatus.PENDING
                activity["scan_triggered"] = False
                return
            
            # Schedule unified file processing
            from huey_worker.unified_ddr_file_processor import process_ddr_file, unified_ddr_huey
            
            logger.info(f"Scheduling DDR file processing for activity {activity.get('id', '')}")
            
            task = process_ddr_file.s(
                task_id=self.ddr_task_id,
                file_info=file_info,
                params=params,
                backlog_hash=backlog_hash
            )
            
            unified_ddr_huey.enqueue(task)
            
        except Exception as e:
            logger.error(f"Error scheduling file processing: {e}")
            activity["status"] = ActivityStatus.PENDING
            activity["scan_triggered"] = False
    
    def _build_file_info(self, activity: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Build file info from DDR activity
        
        Args:
            activity: Activity data
            
        Returns:
            dict: File info or None if invalid
        """
        try:
            ddr_fields = activity.get("ddr_fields", {})
            display_path = ddr_fields.get("display_path", "")
            file_name = ddr_fields.get("file_name", "")
            folder = ddr_fields.get("folder", "")
            
            if not display_path or not folder:
                return None
            
            if not file_name and "/" in display_path:
                file_name = display_path.rsplit('/', 1)[-1]
            
            return {
                "folder": folder,
                "file_name": file_name,
                "display_path": display_path,
                "event_type": activity.get("event_type", "unknown"),
                "actor_id": ddr_fields.get("actor_id", "unknown"),
                "file_size": ddr_fields.get("file_size", 0),
                "file_ext": ddr_fields.get("file_ext", "")
            }
            
        except Exception as e:
            logger.error(f"Error building file info: {e}")
            return None
