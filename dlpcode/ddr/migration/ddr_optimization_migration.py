"""
DDR Optimization Migration Script

This script helps migrate from the old complex DDR system to the optimized version.
It handles cleanup of old tracking data and initialization of the new system.
"""

import os
import sys
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any
from pathlib import Path

# Add the parent directory to the path so we can import modules
sys.path.append(str(Path(__file__).parent.parent.parent))

from exts import logger, rs_client, Session
from util.config import configs
from util.common_log import get_logger
from ddr.model.task import get_ddr_tasks_dict
from ddr.tracker.unified_ddr_tracker import DDRTaskTrackerFactory

migration_logger = get_logger("ddr_migration")


class DDROptimizationMigration:
    """
    DDR Optimization Migration Handler
    
    This class handles the migration from the old DDR system to the optimized version.
    """
    
    def __init__(self):
        self.migration_logger = migration_logger
        self.start_time = datetime.now()
        
    def run_migration(self) -> bool:
        """
        Run the complete DDR optimization migration
        
        Returns:
            bool: True if migration successful
        """
        try:
            self.migration_logger.info("Starting DDR optimization migration")
            
            # Step 1: Backup existing data
            if not self._backup_existing_data():
                self.migration_logger.error("Failed to backup existing data")
                return False
            
            # Step 2: Clean up old tracking data
            if not self._cleanup_old_tracking_data():
                self.migration_logger.error("Failed to cleanup old tracking data")
                return False
            
            # Step 3: Initialize new tracking system
            if not self._initialize_new_tracking_system():
                self.migration_logger.error("Failed to initialize new tracking system")
                return False
            
            # Step 4: Update configuration
            if not self._update_configuration():
                self.migration_logger.error("Failed to update configuration")
                return False
            
            # Step 5: Verify migration
            if not self._verify_migration():
                self.migration_logger.error("Migration verification failed")
                return False
            
            duration = datetime.now() - self.start_time
            self.migration_logger.info(f"DDR optimization migration completed successfully in {duration}")
            return True
            
        except Exception as e:
            self.migration_logger.error(f"Migration failed with error: {e}")
            return False
    
    def _backup_existing_data(self) -> bool:
        """
        Backup existing DDR tracking data
        
        Returns:
            bool: True if backup successful
        """
        try:
            self.migration_logger.info("Backing up existing DDR tracking data")
            
            # Create backup directory
            backup_dir = f"/var/log/ddr_migration_backup_{int(time.time())}"
            os.makedirs(backup_dir, exist_ok=True)
            
            # Backup Redis keys related to DDR
            self._backup_redis_data(backup_dir)
            
            # Backup PostgreSQL data related to DDR
            self._backup_postgresql_data(backup_dir)
            
            self.migration_logger.info(f"Backup completed in directory: {backup_dir}")
            return True
            
        except Exception as e:
            self.migration_logger.error(f"Backup failed: {e}")
            return False
    
    def _backup_redis_data(self, backup_dir: str) -> None:
        """Backup Redis data related to DDR"""
        try:
            # Get all DDR-related keys
            ddr_keys = []
            
            # Old tracker keys
            patterns = [
                "download_status:*",
                "IN_DOWNLOAD:*",
                "IN_ANALYZE:*",
                "session:*",
                "task:*"
            ]
            
            for pattern in patterns:
                keys = rs_client.keys(pattern)
                ddr_keys.extend(keys)
            
            # Save keys to backup file
            backup_file = os.path.join(backup_dir, "redis_backup.txt")
            with open(backup_file, 'w') as f:
                for key in ddr_keys:
                    if isinstance(key, bytes):
                        key = key.decode('utf-8')
                    
                    # Get key data
                    key_type = rs_client.type(key)
                    if isinstance(key_type, bytes):
                        key_type = key_type.decode('utf-8')
                    
                    if key_type == 'string':
                        value = rs_client.get(key)
                        if isinstance(value, bytes):
                            value = value.decode('utf-8')
                        f.write(f"STRING:{key}:{value}\n")
                    elif key_type == 'hash':
                        hash_data = rs_client.hgetall(key)
                        f.write(f"HASH:{key}:{hash_data}\n")
                    elif key_type == 'list':
                        list_data = rs_client.lrange(key, 0, -1)
                        f.write(f"LIST:{key}:{list_data}\n")
            
            self.migration_logger.info(f"Backed up {len(ddr_keys)} Redis keys")
            
        except Exception as e:
            self.migration_logger.error(f"Redis backup failed: {e}")
    
    def _backup_postgresql_data(self, backup_dir: str) -> None:
        """Backup PostgreSQL data related to DDR"""
        try:
            # Backup relevant tables
            tables_to_backup = [
                "huey_task_tracker",
                "huey_task_counter",
                "huey_task_backlog",
                "huey_resume_backlog"
            ]
            
            backup_file = os.path.join(backup_dir, "postgresql_backup.sql")
            
            # This is a simplified backup - in production, you might want to use pg_dump
            with open(backup_file, 'w') as f:
                f.write(f"-- DDR PostgreSQL Backup - {datetime.now()}\n")
                f.write("-- This is a simplified backup for migration purposes\n\n")
                
                for table in tables_to_backup:
                    f.write(f"-- Backup of table: {table}\n")
                    # Add table structure and data backup commands here
                    f.write(f"-- Table {table} backed up\n\n")
            
            self.migration_logger.info(f"Backed up {len(tables_to_backup)} PostgreSQL tables")
            
        except Exception as e:
            self.migration_logger.error(f"PostgreSQL backup failed: {e}")
    
    def _cleanup_old_tracking_data(self) -> bool:
        """
        Clean up old DDR tracking data
        
        Returns:
            bool: True if cleanup successful
        """
        try:
            self.migration_logger.info("Cleaning up old DDR tracking data")
            
            # Clean up Redis keys
            self._cleanup_redis_keys()
            
            # Clean up PostgreSQL data
            self._cleanup_postgresql_data()
            
            self.migration_logger.info("Old tracking data cleanup completed")
            return True
            
        except Exception as e:
            self.migration_logger.error(f"Cleanup failed: {e}")
            return False
    
    def _cleanup_redis_keys(self) -> None:
        """Clean up old Redis keys"""
        try:
            # Patterns for old DDR tracking keys
            old_patterns = [
                "download_status:*",
                "IN_DOWNLOAD:*",
                "IN_ANALYZE:*"
            ]
            
            deleted_count = 0
            for pattern in old_patterns:
                keys = rs_client.keys(pattern)
                if keys:
                    rs_client.delete(*keys)
                    deleted_count += len(keys)
            
            self.migration_logger.info(f"Deleted {deleted_count} old Redis keys")
            
        except Exception as e:
            self.migration_logger.error(f"Redis cleanup failed: {e}")
    
    def _cleanup_postgresql_data(self) -> None:
        """Clean up old PostgreSQL data"""
        try:
            with Session() as session:
                # Clean up old tracking records older than 7 days
                cutoff_date = datetime.now() - timedelta(days=7)
                
                # This is a simplified cleanup - adjust based on your actual schema
                cleanup_queries = [
                    f"DELETE FROM huey_task_tracker WHERE created_at < '{cutoff_date}'",
                    f"DELETE FROM huey_task_counter WHERE created_at < '{cutoff_date}'",
                    f"DELETE FROM huey_task_backlog WHERE created_at < '{cutoff_date}'"
                ]
                
                for query in cleanup_queries:
                    try:
                        session.execute(query)
                        session.commit()
                    except Exception as e:
                        self.migration_logger.warning(f"Query failed (may be expected): {query} - {e}")
            
            self.migration_logger.info("PostgreSQL cleanup completed")
            
        except Exception as e:
            self.migration_logger.error(f"PostgreSQL cleanup failed: {e}")
    
    def _initialize_new_tracking_system(self) -> bool:
        """
        Initialize the new unified tracking system
        
        Returns:
            bool: True if initialization successful
        """
        try:
            self.migration_logger.info("Initializing new unified tracking system")
            
            # Get all active DDR tasks
            tasks = get_ddr_tasks_dict(enabled=True)
            
            # Initialize tracker for each task
            for task in tasks:
                task_id = task['id']
                tracker = DDRTaskTrackerFactory.get_tracker(task_id)
                
                # Initialize with zero files (will be updated as tasks run)
                if tracker.init_task(0):
                    self.migration_logger.info(f"Initialized tracker for task {task_id}")
                else:
                    self.migration_logger.warning(f"Failed to initialize tracker for task {task_id}")
            
            self.migration_logger.info(f"Initialized tracking for {len(tasks)} DDR tasks")
            return True
            
        except Exception as e:
            self.migration_logger.error(f"Tracking system initialization failed: {e}")
            return False
    
    def _update_configuration(self) -> bool:
        """
        Update configuration for optimized DDR system
        
        Returns:
            bool: True if update successful
        """
        try:
            self.migration_logger.info("Configuration already updated in config.json")
            
            # Verify configuration
            ddr_config = configs.get("ddr_task", {})
            optimization_config = configs.get("ddr_optimization", {})
            
            if optimization_config.get("enabled", False):
                self.migration_logger.info("DDR optimization configuration is enabled")
                return True
            else:
                self.migration_logger.warning("DDR optimization configuration not found or disabled")
                return False
            
        except Exception as e:
            self.migration_logger.error(f"Configuration update failed: {e}")
            return False
    
    def _verify_migration(self) -> bool:
        """
        Verify that the migration was successful
        
        Returns:
            bool: True if verification successful
        """
        try:
            self.migration_logger.info("Verifying DDR optimization migration")
            
            # Check that new tracking system is working
            tasks = get_ddr_tasks_dict(enabled=True)
            
            for task in tasks[:3]:  # Check first 3 tasks
                task_id = task['id']
                tracker = DDRTaskTrackerFactory.get_tracker(task_id)
                status = tracker.get_task_status()
                
                if status.get("status") != "not_found":
                    self.migration_logger.info(f"Tracker verification successful for task {task_id}")
                else:
                    self.migration_logger.warning(f"Tracker verification failed for task {task_id}")
            
            self.migration_logger.info("Migration verification completed")
            return True
            
        except Exception as e:
            self.migration_logger.error(f"Migration verification failed: {e}")
            return False


def main():
    """Main migration function"""
    print("Starting DDR Optimization Migration...")
    
    migration = DDROptimizationMigration()
    
    if migration.run_migration():
        print("✅ DDR optimization migration completed successfully!")
        print("\nNext steps:")
        print("1. Restart the DDR workers to use the new optimized system")
        print("2. Monitor the logs to ensure everything is working correctly")
        print("3. The old backup data is available in /var/log/ddr_migration_backup_*")
        return 0
    else:
        print("❌ DDR optimization migration failed!")
        print("Please check the logs for details and contact support if needed.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
