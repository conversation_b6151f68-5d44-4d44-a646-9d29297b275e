version: '3'
services:
  buildenv:
    #image: dops-registry.fortinet-us.com/fimg/buildenv:FortiDLP-01.00.15
    image: 7be1cfa48446
    working_dir: /home/<USER>
    privileged: true
    volumes:
      - ../:/home/<USER>
    environment:
      - GOPROXY=https://proxy.golang.org,direct
      - GOSUMDB=sum.golang.org
      - NG_CLI_ANALYTICS=false
    command: /bin/bash -c "ln -s /lib /lib64 && ln -s /usr/lib /usr/lib64 && /bin/bash" 
